# Sử dụng Node.js LTS (Long Term Support)
FROM node:20-alpine AS builder

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt các dependencies cần thiết cho build
RUN apk add --no-cache python3 make g++

# Sao chép package.json và package-lock.json (hoặc yarn.lock)
COPY package*.json ./

# Cài đặt tất cả dependencies (bao gồm cả devDependencies)
# Sử dụng npm install thay vì npm ci vì package.json và package-lock.json không đồng bộ
RUN npm install

# Sao chép mã nguồn
COPY . .

# Build ứng dụng
RUN npm run build

# Loại bỏ các devDependencies để giảm kích thước image
RUN npm prune --production

# Giai đoạn 2: Tạo image production
FROM node:20-alpine AS production

# Thiết lập thư mục làm việc
WORKDIR /app

# Cài đặt các dependencies cần thiết cho production
RUN apk add --no-cache dumb-init

# Sao chép package.json và package-lock.json từ builder stage
COPY --from=builder /app/package*.json ./

# Sao chép node_modules từ builder stage
# Điều này giúp tránh phải cài đặt lại các dependencies
COPY --from=builder /app/node_modules ./node_modules

# Sao chép các file đã build từ giai đoạn trước
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/.mastra ./.mastra

# Sao chép file .env.example (sẽ được ghi đè bởi biến môi trường thực tế)
COPY .env.example ./.env

# Thiết lập biến môi trường cho production
ENV NODE_ENV=production \
    PORT=3000

# Expose cổng mà ứng dụng sẽ chạy
EXPOSE 3000

# Sử dụng dumb-init để xử lý tín hiệu một cách chính xác
ENTRYPOINT ["dumb-init", "--"]

# Khởi động ứng dụng
CMD ["npm","run", "start:express:prod"]
