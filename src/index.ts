import express, { Request, Response } from "express";
import cors from "cors";
import bodyParser from "body-parser";
import dotenv from "dotenv";
import routes from "./routes/index";
import { errorMiddleware } from "./middlewares/index";
import { getWeaviateClientInstance } from "./config/weaviate";
import { initializeBullMQSystem, shutdownBullMQSystem, createHealthCheckEndpoint, createMetricsEndpoint, productSyncQueue } from "./services/queue";
import { getBullBoardRouter } from "./services/queue/bull-board.service";
import { bullBoardDevMiddleware } from "./middlewares/bull-board-auth.middleware";
import { exec } from "child_process";
import { promisify } from "util";

// Khởi tạo biến môi trường
dotenv.config();

// Promisify exec để sử dụng async/await
const execAsync = promisify(exec);

/**
 * Kiểm tra và kill process đang sử dụng port
 */
const killPortIfInUse = async (port: number): Promise<void> => {
  try {
    console.log(`🔍 Kiểm tra port ${port}...`);
    
    // Tìm process đang sử dụng port
    const { stdout } = await execAsync(`lsof -ti:${port}`);
    
    if (stdout.trim()) {
      const pids = stdout.trim().split('\n');
      console.log(`⚠️ Phát hiện ${pids.length} process đang sử dụng port ${port}: ${pids.join(', ')}`);
      
      // Kill tất cả process đang sử dụng port
      for (const pid of pids) {
        if (pid.trim()) {
          try {
            await execAsync(`kill -9 ${pid.trim()}`);
            console.log(`✅ Đã kill process ${pid.trim()}`);
          } catch (killError) {
            console.log(`⚠️ Không thể kill process ${pid.trim()}: ${killError}`);
          }
        }
      }
      
      // Đợi một chút để đảm bảo port được giải phóng
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log(`✅ Port ${port} đã được giải phóng`);
    } else {
      console.log(`✅ Port ${port} đang trống, sẵn sàng sử dụng`);
    }
  } catch (error: any) {
    // Nếu lsof không tìm thấy process nào (exit code 1), có nghĩa là port đang trống
    if (error.code === 1) {
      console.log(`✅ Port ${port} đang trống, sẵn sàng sử dụng`);
    } else {
      console.error(`❌ Lỗi khi kiểm tra port ${port}:`, error.message);
    }
  }
};

/**
 * Khởi tạo và cấu hình Express app
 */
const createApp = () => {
  const app = express();

  // Middleware
  app.use(cors());
  app.use(bodyParser.json());

  // Route mặc định
  app.get("/", (_req: Request, res: Response) => {
    res.send("Hello, TypeScript Express!");
  });

  // BullMQ Health check endpoints
  app.get("/health", createHealthCheckEndpoint(productSyncQueue));
  app.get("/metrics", createMetricsEndpoint());

  // Bull Board Dashboard - Đặt trước routes chính để tránh conflict
  try {
    // Sử dụng authentication middleware cho Bull Board
    app.use('/admin/queues', bullBoardDevMiddleware, getBullBoardRouter());
    console.log('🎛️ Bull Board Dashboard đã được khởi tạo tại: /admin/queues');
    console.log('🔐 Bull Board được bảo vệ bằng Basic Authentication');
    console.log(`👤 Username: ${process.env.BULL_BOARD_USERNAME || 'admin'}`);
    console.log(`🔑 Password: ${process.env.BULL_BOARD_PASSWORD ? '***' : 'mooly123'}`);
  } catch (error) {
    console.error('❌ Lỗi khi khởi tạo Bull Board:', error);
  }

  // Routes
  app.use("/api", routes);

  // Error handling middleware (luôn đặt ở cuối)
  app.use(errorMiddleware);

  return app;
};

/**
 * Kiểm tra kết nối với Weaviate server
 */
const checkWeaviateConnection = async () => {
  try {
    await getWeaviateClientInstance();
    console.log("✅ Kết nối Weaviate thành công");
    return true;
  } catch (error) {
    console.error("❌ Không thể kết nối đến Weaviate server:", error);
    return false;
  }
};

// Import cấu hình BullMQ mới
import { loggingConfig } from './services/queue/config';

/**
 * Khởi động server
 */
const startServer = async (app: express.Application) => {
  const PORT = process.env.PORT || 3003;

  // Kiểm tra và kill port nếu đang được sử dụng
  await killPortIfInUse(Number(PORT));

  // Kiểm tra kết nối Weaviate trước khi khởi động server
  await checkWeaviateConnection();

  // Kiểm tra xem có tắt BullMQ không
  const isBullMQDisabled = process.env.DISABLE_BULLMQ === 'true';
  const isIntegratedMode = process.env.INTEGRATED_MODE === 'true';

  if (isBullMQDisabled) {
    console.log('⚠️ BullMQ đã bị tắt theo cấu hình, không khởi động workers');
    console.log('⚠️ Các tác vụ đồng bộ sản phẩm sẽ được xử lý đồng bộ trực tiếp');
  } else if (isIntegratedMode) {
    console.log('🔄 Chế độ tích hợp: BullMQ worker sẽ chạy riêng biệt');
    console.log('⚠️ Không khởi động worker trong Express server để tránh xung đột');
  } else {
    // Khởi động BullMQ system
    try {
      await initializeBullMQSystem();
      console.log('✅ BullMQ system đã được khởi động');
    } catch (error: any) {
      console.error('❌ Lỗi khi khởi động BullMQ workers:', error);

      // Kiểm tra xem có phải lỗi kết nối Redis/Dragonfly không
      const isRedisConnectionError =
        error.message && (
          error.message.includes('ECONNREFUSED') ||
          error.message.includes('connection is closed') ||
          error.message.includes('Redis connection lost') ||
          error.message.includes('Dragonfly connection lost') ||
          error.message.includes('Timeout') ||
          error.message.includes('timed out')
        );

      if (isRedisConnectionError) {
        console.log('⚠️ Không thể kết nối đến Redis/Dragonfly, hệ thống sẽ xử lý đồng bộ trực tiếp');
        console.log('⚠️ Vui lòng kiểm tra lại cấu hình Redis/Dragonfly trong file .env:');
        console.log(`   REDIS_HOST=${process.env.REDIS_HOST}`);
        console.log(`   REDIS_PORT=${process.env.REDIS_PORT}`);
        console.log(`   REDIS_PASSWORD=${process.env.REDIS_PASSWORD ? '***' : 'không có'}`);
      }

      // Tiếp tục khởi động server ngay cả khi worker không khởi động được
      console.log('⚠️ Server vẫn sẽ được khởi động mặc dù có lỗi với BullMQ workers');
      console.log('⚠️ Các tác vụ đồng bộ sản phẩm sẽ được xử lý đồng bộ (không qua queue)');
    }
  }

  // Xử lý tắt server một cách an toàn
  process.on("SIGTERM", async () => {
    console.log("Nhận tín hiệu SIGTERM, đang dừng server...");
    await shutdownBullMQSystem();
    process.exit(0);
  });

  process.on("SIGINT", async () => {
    console.log("Nhận tín hiệu SIGINT, đang dừng server...");
    await shutdownBullMQSystem();
    process.exit(0);
  });

  return app.listen(PORT, () => {
    console.log(`Server đang chạy trên cổng ${PORT}`);
  });
};

// Khởi tạo Express app
const app = createApp();

// Khởi động server và kiểm tra kết nối Weaviate
startServer(app).catch((error) => {
  console.error("Lỗi khi khởi động server:", error);
  process.exit(1);
});
