import { getWeaviateClientInstance } from "../../config/weaviate";

/**
 * Khởi tạo Weaviate client cho Mastra
 * Hàm này sẽ được gọi khi Mastra khởi động
 */
export async function initWeaviateForMastra() {
  try {
    // Khởi tạo Weaviate client
    const client = await getWeaviateClientInstance();
    console.log("✅ Đã khởi tạo Weaviate client cho Mastra thành công");
    return client;
  } catch (error) {
    console.warn("⚠️ Không thể khởi tạo Weaviate client cho Mastra:", error);
    console.warn("⚠️ Các chức năng liên quan đến Weaviate sẽ không hoạt động");
    // Trả về null để Mastra có thể tiếp tục hoạt động
    return null;
  }
}

export default initWeaviateForMastra;
