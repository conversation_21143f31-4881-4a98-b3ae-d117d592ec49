import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { updateOrderStatus } from "../../../services/supabase/product.service";

/**
 * Công cụ cập nhật trạng thái đơn hàng
 * Sử dụng status enum thay vì status_id
 */
export const updateOrderStatusTool = createTool({
  id: "update_order_status",
  description: "Cập nhật trạng thái đơn hàng",
  inputSchema: z.object({
    order_id: z.string().describe("ID của đơn hàng cần cập nhật"),
    status: z.enum(["pending", "processing", "shipped", "delivered", "cancelled"]).describe("Trạng thái mới của đơn hàng"),
    comment: z.string().describe("Ghi chú về việc thay đổi trạng thái"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("<PERSON><PERSON> cập nhật trạng thái đơn hàng:", context);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Cập nhật trạng thái đơn hàng
      const result = await updateOrderStatus({
        order_id: context.order_id,
        status: context.status,
        comment: context.comment,
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        console.error(`Không thể cập nhật trạng thái đơn hàng: ${result.message}`);
        return {
          success: false,
          error: result.message,
        };
      }

      return {
        success: true,
        data: result.data,
        message: `Đã cập nhật trạng thái đơn hàng thành ${context.status}`,
      };
    } catch (error: any) {
      console.error("Lỗi khi cập nhật trạng thái đơn hàng:", error);
      return {
        success: false,
        error: `Lỗi khi cập nhật trạng thái đơn hàng: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
