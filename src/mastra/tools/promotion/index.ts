import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getActivePromotions } from "../../../services/supabase/product.service";

/**
 * Công cụ lấy danh sách khuyến mãi đang hoạt động
 * Sử dụng Supabase để lấy thông tin khuyến mãi
 */
export const getPromotionsTool = createTool({
  id: "get_promotions",
  description: "Lấy danh sách khuyến mãi đang hoạt động",
  inputSchema: z.object({
    // promotion_code: z.string().optional().describe("Mã khuyến mãi cụ thể (nếu có)"),
    // product_id: z.string().optional().describe("ID sản phẩm để lọc khuyến mãi áp dụng cho sản phẩm cụ thể (nếu có)"),
    // category_id: z.string().optional().describe("ID danh mục để lọc khuyến mãi áp dụng cho danh mục cụ thể (nếu có)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("Đang lấy danh sách khuyến mãi đang hoạt động");

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          promotions: [],
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Lấy danh sách khuyến mãi từ Supabase
      const result = await getActivePromotions({
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        return {
          promotions: [],
          error: result.message,
        };
      }

      return {
        promotions: result.data || [],
        total: result.data?.length || 0,
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy danh sách khuyến mãi:", error);
      return {
        promotions: [],
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  },
});
