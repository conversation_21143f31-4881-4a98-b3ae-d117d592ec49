import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// 1. <PERSON>ông cụ chuyển khách hàng sang nhân viên thật
export const humanHandoffTool = createTool({
  id: "human_handoff",
  description: "Chuy<PERSON>n khách hàng sang nhân viên thực",
  inputSchema: z.object({
    reason: z.string().describe("Lý do chuyển giao"),
    conversation_history: z
      .string()
      .optional()
      .describe("Lịch sử cuộc trò chuyện"),
  }),
  execute: async ({ context, runtimeContext }) => {
    console.log(runtimeContext.get("tenant_id"));
    console.log("Đang chuyển khách hàng sang nhân viên thật:", context.reason);

    // Chuyển giao sang nhân viên thật
    const handoffStatus = {
      status: "approved",
      message: "<PERSON><PERSON><PERSON> cầu của bạn đã được chuyển sang nhân viên. Nhân viên sẽ chủ động liên hệ với bạn trong thời gian sớm nhất.",
      estimated_response_time: "15 phút",
    };

    // Kiểm tra lý do chuyển giao
    if (
      context.reason.toLowerCase().includes("spam") ||
      context.reason.toLowerCase().includes("quấy rối")
    ) {
      handoffStatus.status = "approved";
      handoffStatus.message = "Yêu cầu của bạn đã được chuyển sang bộ phận xử lý. Nhân viên sẽ xem xét và liên hệ nếu cần thiết.";
      handoffStatus.estimated_response_time = "N/A";

      return {
        human_handoff: {
          ...handoffStatus,
          action: "terminate_conversation",
          reason: context.reason,
        },
      };
    }

    return {
      human_handoff: handoffStatus,
    };
  },
});

// 2. Công cụ phát hiện spam
export const detectSpamTool = createTool({
  id: "detect_spam",
  description: "Phát hiện tin nhắn spam hoặc quấy rối",
  inputSchema: z.object({}),
  execute: async ({ runtimeContext, context }) => {
    console.log("Đang phát hiện spam");
    console.log(runtimeContext.get("tenant_id"));
    // Giả lập phát hiện spam (luôn trả về không phải spam)
    return {
      spam_detection: {
        is_spam: false,
        confidence: 0.05,
        category: "normal_conversation",
        recommendation: "continue",
      },
    };
  },
});
