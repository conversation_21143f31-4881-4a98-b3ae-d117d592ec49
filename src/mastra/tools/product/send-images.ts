import { createTool } from "@mastra/core/tools";
import { z } from "zod";

/**
 * Công cụ gửi hình ảnh cho khách hàng
 * Sử dụng Supabase để gửi hình ảnh cho khách hàng
 */
export const sendImagesTool = createTool({
  id: "send_images",
  description: "Gửi hình ảnh cho khách hàng",
  inputSchema: z.object({
    images: z.array(z.string()).describe("Danh sách URL của các hình ảnh cần gửi cho khách hàng"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("Đang gửi hình ảnh cho khách hàng:", context);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      // console.log(runtimeContext)

      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // G<PERSON>i hình ảnh cho khách hàng
    //   const result = await sendImages({
    //     images: context.images,
    //     tenant_id: tenant_id.toString(),
    //   });
    console.log('Đã gửi: ', context.images);

    //   if (!result.success) {
    //     console.error(`Không thể gửi hình ảnh: ${result.message}`);
    //     return {
    //       success: false,
    //       error: result.message,
    //     };
    //   }

      return {
        success: true,
        // data: result.data,
        message: "Đã gửi hình ảnh thành công cho người dùng",
      };
    } catch (error: any) {
      console.error("Lỗi khi gửi hình ảnh:", error);
      return {
        success: false,
        error: `Lỗi khi gửi hình ảnh: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
