import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getProductCategories } from "../../../services/supabase/product.service";

/**
 * Công cụ lấy danh sách danh mục sản phẩm
 * Sử dụng Supabase để lấy danh sách danh mục
 */
export const getProductCategoriesTool = createTool({
  id: "get_product_categories",
  description: "Lấy danh sách danh mục sản phẩm",
  inputSchema: z.object({}),
  execute: async ({ runtimeContext }) => {
    try {
      console.log("Đang lấy danh sách danh mục sản phẩm");

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");

      if (!tenant_id) {
        return {
          categories: [],
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // L<PERSON>y danh sách danh mục sản phẩm từ Supabase
      const result = await getProductCategories({
        tenant_id: tenant_id.toString(),
      });

      if (!result.success) {
        return {
          categories: [],
          error: result.message,
        };
      }

      return {
        categories: result.data,
      };
    } catch (error: any) {
      console.error("Lỗi khi lấy danh sách danh mục sản phẩm:", error);
      return {
        categories: [],
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  },
});
