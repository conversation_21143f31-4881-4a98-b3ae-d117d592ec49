import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { sql } from "../../../config/postgres";

/**
 * @deprecated TOOL NÀY ĐÃ ĐƯỢC THAY THẾ BỞI getProductDetailsProductionTool
 *
 * TOOL LẤY THÔNG TIN SẢN PHẨM VÀ GIÁ PRODUCTION - DUY NHẤT CHO HỆ THỐNG
 *
 * ⚠️ DEPRECATED: Sử dụng getProductDetailsProductionTool thay thế
 * Tool này tích hợp đầy đủ tính năng:
 * - Tự động xử lý logic giá cho sản phẩm có/không có biến thể
 * - Hiển thị giá sale và giá gốc
 * - Kiểm tra tồn kho
 * - Hiển thị tất cả biến thể nếu sản phẩm có nhiều biến thể
 */
export const getProductPricingProductionTool = createTool({
  id: "get_product_pricing_production",
  description: "<PERSON><PERSON>y thông tin sản phẩm và giá production - Tool duy nhất: tự động xử lý logic giá cho sản phẩm có/không có biến thể, hiển thị giá sale, kiểm tra tồn kho",
  inputSchema: z.object({
    product_id: z.string().describe("Mã sản phẩm"),
    variant_id: z
      .string()
      .nullable()
      .optional()
      .describe("Mã biến thể sản phẩm (tùy chọn - nếu không có sẽ hiển thị tất cả biến thể)"),
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log(`🔍 [PRODUCTION] Đang lấy thông tin sản phẩm: ${context.product_id}`);

      // Lấy tenant_id từ runtime context
      const tenant_id = runtimeContext.get("tenant_id");
      if (!tenant_id) {
        return {
          success: false,
          error: "Thiếu thông tin tenant_id trong runtime context",
        };
      }

      // Kiểm tra xem có variant_id không
      if (context.variant_id) {
        // TRƯỜNG HỢP 1: Lấy thông tin biến thể cụ thể
        console.log(`📦 Lấy thông tin biến thể: ${context.variant_id}`);

        const variantResult = await (sql as any)`
          SELECT
            v.id, v.name as variant_name, v.sku, v.price, v.sale_price, v.stock_quantity, v.is_active, v.attributes,
            p.id as product_id, p.name as product_name, p.description, p.short_description, p.type as product_type,
            p.avatar, p.images, p.is_active as product_is_active, p.category_id
          FROM product_variants v
          JOIN products p ON v.product_id = p.id
          WHERE v.id = ${context.variant_id} AND v.product_id = ${context.product_id}
            AND v.tenant_id = ${tenant_id} AND v.is_active = true AND p.is_active = true
        `;

        if (!variantResult || variantResult.length === 0) {
          return {
            success: false,
            error: `❌ Không tìm thấy biến thể sản phẩm với ID: ${context.variant_id}`,
          };
        }

        const variant = variantResult[0];
        const originalPrice = parseFloat(variant.price);
        const finalPrice = variant.sale_price ? parseFloat(variant.sale_price) : originalPrice;
        const isOnSale = variant.sale_price && parseFloat(variant.sale_price) < originalPrice;
        const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

        return {
          success: true,
          product_info: {
            product_id: variant.product_id,
            variant_id: variant.id,
            product_name: variant.product_name,
            variant_name: variant.variant_name,
            description: variant.description,
            short_description: variant.short_description,
            sku: variant.sku,
            type: variant.product_type,
            avatar: variant.avatar,
            images: variant.images,
            category_id: variant.category_id,

            // Thông tin giá chi tiết
            pricing: {
              original_price: originalPrice,
              final_price: finalPrice,
              is_on_sale: isOnSale,
              discount_percentage: discountPercentage,
              price_display: isOnSale ?
                `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
                `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
              savings: isOnSale ? originalPrice - finalPrice : 0,
              savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
            },

            // Thông tin tồn kho
            stock_info: {
              stock_quantity: variant.stock_quantity,
              in_stock: variant.stock_quantity > 0,
              stock_status: variant.stock_quantity > 10 ? 'Còn hàng' :
                           variant.stock_quantity > 0 ? `Chỉ còn ${variant.stock_quantity} sản phẩm` : 'Hết hàng',
            },

            attributes: variant.attributes,
            has_variants: true,
          },
          message: `✅ Biến thể "${variant.variant_name}" - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
        };
      } else {
        // TRƯỜNG HỢP 2: Lấy thông tin sản phẩm (có thể có nhiều biến thể)
        console.log(`📦 Lấy thông tin sản phẩm: ${context.product_id}`);

        const productResult = await (sql as any)`
          SELECT id, name, description, short_description, sku, price, sale_price, stock_quantity, type,
                 avatar, images, is_active, category_id
          FROM products
          WHERE id = ${context.product_id} AND tenant_id = ${tenant_id} AND is_active = true
        `;

        if (!productResult || productResult.length === 0) {
          return {
            success: false,
            error: `❌ Không tìm thấy sản phẩm với ID: ${context.product_id}`,
          };
        }

        const product = productResult[0];

        if (product.type === 'variable') {
          // Sản phẩm có biến thể - lấy tất cả biến thể
          console.log(`🔄 Sản phẩm có biến thể, đang lấy tất cả biến thể...`);

          const allVariants = await (sql as any)`
            SELECT id, name, sku, price, sale_price, stock_quantity, attributes
            FROM product_variants
            WHERE product_id = ${context.product_id} AND tenant_id = ${tenant_id} AND is_active = true
            ORDER BY name
          `;

          const variants = allVariants.map((v: any) => {
            const originalPrice = parseFloat(v.price);
            const finalPrice = v.sale_price ? parseFloat(v.sale_price) : originalPrice;
            const isOnSale = v.sale_price && parseFloat(v.sale_price) < originalPrice;
            const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

            return {
              id: v.id,
              name: v.name,
              sku: v.sku,
              pricing: {
                original_price: originalPrice,
                final_price: finalPrice,
                is_on_sale: isOnSale,
                discount_percentage: discountPercentage,
                price_display: isOnSale ?
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}%)` :
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
                savings: isOnSale ? originalPrice - finalPrice : 0,
              },
              stock_info: {
                stock_quantity: v.stock_quantity,
                in_stock: v.stock_quantity > 0,
                stock_status: v.stock_quantity > 10 ? 'Còn hàng' :
                             v.stock_quantity > 0 ? `Còn ${v.stock_quantity}` : 'Hết hàng',
              },
              attributes: v.attributes,
            };
          });

          // Tính giá range
          const prices = variants.map((v: any) => v.pricing.final_price);
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);

          return {
            success: true,
            product_info: {
              product_id: product.id,
              product_name: product.name,
              description: product.description,
              short_description: product.short_description,
              sku: product.sku,
              type: product.type,
              avatar: product.avatar,
              images: product.images,
              category_id: product.category_id,

              // Thông tin giá range
              price_range: {
                min_price: minPrice,
                max_price: maxPrice,
                price_display: minPrice === maxPrice ?
                  `${minPrice.toLocaleString('vi-VN')} VNĐ` :
                  `${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ`,
              },

              // Thông tin tồn kho tổng
              stock_info: {
                total_stock: variants.reduce((sum: any, v: { stock_info: { stock_quantity: any; }; }) => sum + v.stock_info.stock_quantity, 0),
                available_variants: variants.filter((v: { stock_info: { in_stock: any; }; }) => v.stock_info.in_stock).length,
                total_variants: variants.length,
              },

              variants: variants,
              has_variants: true,
            },
            message: `✅ Sản phẩm "${product.name}" có ${variants.length} biến thể. Giá từ ${minPrice.toLocaleString('vi-VN')} - ${maxPrice.toLocaleString('vi-VN')} VNĐ. Vui lòng chọn biến thể cụ thể để đặt hàng.`,
          };
        } else {
          // Sản phẩm đơn giản - không có biến thể
          console.log(`📦 Sản phẩm đơn giản`);

          const originalPrice = parseFloat(product.price);
          const finalPrice = product.sale_price ? parseFloat(product.sale_price) : originalPrice;
          const isOnSale = product.sale_price && parseFloat(product.sale_price) < originalPrice;
          const discountPercentage = isOnSale ? Math.round(((originalPrice - finalPrice) / originalPrice) * 100) : 0;

          return {
            success: true,
            product_info: {
              product_id: product.id,
              product_name: product.name,
              description: product.description,
              short_description: product.short_description,
              sku: product.sku,
              type: product.type,
              avatar: product.avatar,
              images: product.images,
              category_id: product.category_id,

              // Thông tin giá chi tiết
              pricing: {
                original_price: originalPrice,
                final_price: finalPrice,
                is_on_sale: isOnSale,
                discount_percentage: discountPercentage,
                price_display: isOnSale ?
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ (Giảm ${discountPercentage}% từ ${originalPrice.toLocaleString('vi-VN')} VNĐ)` :
                  `${finalPrice.toLocaleString('vi-VN')} VNĐ`,
                savings: isOnSale ? originalPrice - finalPrice : 0,
                savings_display: isOnSale ? `Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ` : null,
              },

              // Thông tin tồn kho
              stock_info: {
                stock_quantity: product.stock_quantity,
                in_stock: product.stock_quantity > 0,
                stock_status: product.stock_quantity > 10 ? 'Còn hàng' :
                             product.stock_quantity > 0 ? `Chỉ còn ${product.stock_quantity} sản phẩm` : 'Hết hàng',
              },

              has_variants: false,
            },
            message: `✅ Sản phẩm "${product.name}" - Giá: ${finalPrice.toLocaleString('vi-VN')} VNĐ${isOnSale ? ` (Tiết kiệm ${(originalPrice - finalPrice).toLocaleString('vi-VN')} VNĐ)` : ''}`,
          };
        }
      }
    } catch (error: any) {
      console.error("❌ Lỗi khi lấy thông tin sản phẩm production:", error);
      return {
        success: false,
        error: `Lỗi khi lấy thông tin sản phẩm: ${error?.message || "Lỗi không xác định"}`,
      };
    }
  },
});
