import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { getProducts, getProductBySku, getProductDetailsByIds } from "../../../services/supabase/product.service";
import { searchProducts } from "../../../services/weaviate/product.service";

/**
 * Tối ưu hóa dữ liệu sản phẩm để tiết kiệm token cho AI
 * Chỉ giữ lại các field cần thiết và giới hạn kích thước dữ liệu
 */
const optimizeProductData = (products: any[]) => {
  if (!Array.isArray(products)) {
    return [];
  }

  return products
    .filter(product => product && product.id) // Lọc ra các sản phẩm hợp lệ
    .map(product => ({
      id: String(product.id),
      name: String(product.name || "Không có tên"),
      price: Number(product.price) || 0,
      sale_price: product.sale_price ? Number(product.sale_price) : undefined,
      stock_quantity: Number(product.stock_quantity) || 0,
      short_description: String(product.short_description || product.description?.substring(0, 200) || "Không có mô tả"),
      avatar: product.avatar ? String(product.avatar) : undefined,
      images: Array.isArray(product.images) ? product.images.slice(0, 3).map(String) : undefined, // Chỉ lấy tối đa 3 hình ảnh
      type: product.type ? String(product.type) : undefined,
      sku: product.sku ? String(product.sku) : undefined,
      is_active: Boolean(product.is_active !== false), // Default true nếu không có giá trị
      // Chỉ bao gồm variants nếu có và cần thiết
      variants: Array.isArray(product.variants) ? product.variants
        .filter((variant: any) => variant && variant.id)
        .map((variant: any) => ({
          id: String(variant.id),
          name: String(variant.name || "Không có tên variant"),
          price: Number(variant.price) || 0,
          stock_quantity: Number(variant.stock_quantity) || 0,
          sku: variant.sku ? String(variant.sku) : undefined,
          attributes: variant.attributes || {}
        })) : undefined
    }));
};

/**
 * Công cụ tìm kiếm sản phẩm - Tối ưu hóa cho production
 * Sử dụng Weaviate để tìm kiếm vector và Supabase để lấy thông tin chi tiết
 * Chỉ trả về các field cần thiết để tiết kiệm token cho AI
 * Hỗ trợ tìm kiếm theo mô tả hoặc SKU
 */
export const searchProductsTool = createTool({
  id: "search_products",
  description: "Tìm kiếm sản phẩm với từ khóa, SKU hoặc bộ lọc. Trả về thông tin tối ưu cho AI: tên, giá, mô tả ngắn, tồn kho, hình ảnh",
  inputSchema: z.object({
    query: z.string().min(1, "Query không được để trống").describe("Từ khóa tìm kiếm hoặc mã SKU sản phẩm")
  }),
  outputSchema: z.object({
    success: z.boolean().describe("Trạng thái thành công của tìm kiếm"),
    products: z.array(z.object({
      id: z.string(),
      name: z.string(),
      price: z.number(),
      sale_price: z.number().optional(),
      stock_quantity: z.number(),
      short_description: z.string(),
      avatar: z.string().optional(),
      images: z.array(z.string()).optional(),
      type: z.string().optional(),
      sku: z.string().optional(),
      is_active: z.boolean(),
      variants: z.array(z.object({
        id: z.string(),
        name: z.string(),
        price: z.number(),
        stock_quantity: z.number(),
        sku: z.string().optional(),
        attributes: z.record(z.any()).optional()
      })).optional()
    })).describe("Danh sách sản phẩm tìm được"),
    total_results: z.number().describe("Tổng số kết quả tìm được"),
    page: z.number().optional().describe("Trang hiện tại"),
    message: z.string().optional().describe("Thông báo kết quả tìm kiếm"),
    error: z.string().optional().describe("Thông báo lỗi nếu có")
  }),
  execute: async ({ context, runtimeContext }) => {
    try {
      console.log("=== BẮT ĐẦU TÌM KIẾM SẢN PHẨM ===");
      console.log("Query:", context.query);

      // Validate input
      if (!context.query || context.query.trim().length === 0) {
        return {
          success: false,
          message: "Query tìm kiếm không được để trống",
          products: [],
          total_results: 0,
          page: 1,
          error: "Query không hợp lệ"
        };
      }

      // Lấy tenant_id và bot_id từ runtime context
      const tenant_id = runtimeContext?.get("tenant_id") || "shop_123";
      const bot_id = runtimeContext?.get("bot_id") || "assistant_bot_456";

      if (!tenant_id) {
        return {
          success: false,
          message: "Thiếu thông tin tenant_id trong runtime context",
          products: [],
          total_results: 0,
          page: 1,
          error: "Thiếu tenant_id"
        };
      }

      // Kiểm tra xem query có phải là SKU không (thường SKU có định dạng cụ thể)
      // Ví dụ: SKU thường là chuỗi ngắn, có thể chứa chữ và số, không có dấu cách
      const trimmedQuery = context.query.trim();
      const isSku = trimmedQuery && (
        // Kiểm tra nếu query ngắn (thường SKU không quá 20 ký tự)
        (trimmedQuery.length <= 20 && !trimmedQuery.includes(' ')) ||
        // Hoặc nếu query bắt đầu bằng các tiền tố SKU phổ biến
        trimmedQuery.match(/^(SKU|sku|SP|sp|MS|ms|#)[-:]?\w+$/) ||
        // Hoặc nếu query chỉ chứa chữ cái, số và dấu gạch ngang/gạch dưới
        trimmedQuery.match(/^[A-Za-z0-9\-_]+$/)
      );

      console.log(`Query "${trimmedQuery}" ${isSku ? 'có vẻ là SKU' : 'không phải là SKU'}`);

      // Nếu có vẻ là SKU, tìm kiếm trực tiếp trong Supabase
      if (isSku) {
        console.log(`Tìm kiếm sản phẩm theo SKU: ${trimmedQuery}`);
        try {
          const skuResult = await getProductBySku({
            sku: trimmedQuery,
            tenant_id: tenant_id.toString(),
          });

          if (skuResult.success && skuResult.data && Array.isArray(skuResult.data) && skuResult.data.length > 0) {
            // Tối ưu hóa dữ liệu trả về cho tìm kiếm SKU
            const optimizedSkuProducts = optimizeProductData(skuResult.data);

            return {
              success: true,
              products: optimizedSkuProducts,
              total_results: optimizedSkuProducts.length,
              page: 1,
              message: `Tìm thấy ${optimizedSkuProducts.length} sản phẩm với SKU: ${trimmedQuery}`
            };
          }

          // Nếu không tìm thấy theo SKU, tiếp tục tìm kiếm thông thường
          console.log("Không tìm thấy sản phẩm với SKU, tiếp tục tìm kiếm theo mô tả");
        } catch (skuError) {
          console.error("Lỗi khi tìm kiếm theo SKU:", skuError);
          // Tiếp tục với tìm kiếm thông thường thay vì return lỗi
        }
      }

      // Nếu có query (mô tả sản phẩm), tìm kiếm theo văn bản
      if (trimmedQuery) {
        console.log("Tìm kiếm sản phẩm theo mô tả");
        try {
          // Tìm kiếm trong Weaviate trước
          const weaviateResult = await searchProducts(
            {
              query: trimmedQuery,
              image_url: "",
              bot_id: bot_id.toString(),
              tenant_id: tenant_id.toString(),
            },
            1
          );

          // Nếu tìm thấy kết quả trong Weaviate
          if (weaviateResult.success && weaviateResult.data && weaviateResult.data.objects && Array.isArray(weaviateResult.data.objects) && weaviateResult.data.objects.length > 0) {
            const searchResults = weaviateResult.data.objects;
            console.log(`Tìm thấy ${searchResults.length} kết quả từ tìm kiếm văn bản`);

            // Lấy danh sách product_id để truy vấn thông tin chi tiết từ Supabase
            const productIds = searchResults
              .map((item: any) => item.belongsToGroup)
              .filter(Boolean);

            if (productIds.length > 0) {
              console.log(`Tìm thấy ${productIds.length} product_ids từ Weaviate, lấy thông tin chi tiết từ Supabase`);

              try {
                // Sử dụng hàm mới để lấy thông tin chi tiết từ Supabase
                const detailsResult = await getProductDetailsByIds({
                  productIds,
                  tenant_id: tenant_id.toString(),
                  bot_id: bot_id.toString(),
                });

                if (detailsResult.success && detailsResult.data) {
                  // Áp dụng bộ lọc nếu cần
                  let filteredProducts = Array.isArray(detailsResult.data) ? detailsResult.data : [];

                  // Tối ưu hóa dữ liệu trả về - chỉ giữ các field cần thiết cho AI
                  const optimizedProducts = optimizeProductData(filteredProducts);

                  return {
                    success: true,
                    products: optimizedProducts,
                    total_results: optimizedProducts.length,
                    page: 1,
                    message: `Tìm thấy ${optimizedProducts.length} sản phẩm phù hợp với "${trimmedQuery}"`
                  };
                }
              } catch (detailsError) {
                console.error("Lỗi khi lấy thông tin chi tiết sản phẩm:", detailsError);
                return {
                  success: false,
                  products: [],
                  total_results: 0,
                  page: 1,
                  message: "Có lỗi khi lấy thông tin chi tiết sản phẩm",
                  error: detailsError instanceof Error ? detailsError.message : "Lỗi không xác định"
                };
              }
            }
          }
        } catch (searchError) {
          console.error("Lỗi khi tìm kiếm trong Weaviate:", searchError);
          return {
            success: false,
            products: [],
            total_results: 0,
            page: 1,
            message: "Có lỗi khi tìm kiếm sản phẩm",
            error: searchError instanceof Error ? searchError.message : "Lỗi tìm kiếm không xác định"
          };
        }
      }

      // Nếu không tìm thấy kết quả nào
      return {
        success: true,
        products: [],
        total_results: 0,
        page: 1,
        message: `Không tìm thấy sản phẩm nào phù hợp với "${trimmedQuery}"`
      };
    } catch (error) {
      console.error("Lỗi khi tìm kiếm sản phẩm:", error);
      // Trả về dữ liệu trống thay vì gây ra lỗi
      return {
        success: false,
        products: [],
        total_results: 0,
        page: 1,
        message: "Có lỗi xảy ra trong quá trình tìm kiếm",
        error: error instanceof Error ? error.message : "Lỗi không xác định",
      };
    }
  },
});
