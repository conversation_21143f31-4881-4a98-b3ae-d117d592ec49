import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import axios from 'axios';

/**
 * Tool gửi tin nhắn Messenger cho kh<PERSON>ch hàng qua Facebook Graph API
 * Sử dụng để trả lời tin nhắn sau khi khách hàng comment trên fanpage
 */
export const sendMessengerMessageTool = createTool({
  id: 'send_messenger_message',
  description: 'Gửi tin nhắn Messenger cho khách hàng qua Facebook Graph API',
  inputSchema: z.object({
    comment_id: z.string().describe('ID của comment cần trả lời'),
    message: z.string().describe('Nội dung tin nhắn cần gửi'),
    page_access_token: z.string().optional().describe('Access token của Page (nếu không cung cấp sẽ sử dụng token từ biến môi trường)'),
  }),
  execute: async ({ context }) => {
    try {
      console.log('<PERSON><PERSON> gửi tin nhắn Messenger cho comment:', context.comment_id);

      // Sử dụng token từ tham số hoặc biến môi trường
      const pageAccessToken = context.page_access_token || process.env.FACEBOOK_PAGE_ACCESS_TOKEN;

      if (!pageAccessToken) {
        throw new Error('Không tìm thấy Page Access Token. Vui lòng cung cấp token hoặc thiết lập biến môi trường FACEBOOK_PAGE_ACCESS_TOKEN');
      }

      // Gửi private reply đến comment
      const response = await axios({
        method: 'POST',
        url: `https://graph.facebook.com/v18.0/${context.comment_id}/private_replies`,
        params: {
          message: context.message,
          access_token: pageAccessToken
        }
      });

      return {
        success: true,
        message_id: response.data?.id || null,
        response_data: response.data
      };
    } catch (error: any) {
      console.error('Lỗi khi gửi tin nhắn Messenger:', error);

      return {
        success: false,
        error: error.message || 'Lỗi không xác định khi gửi tin nhắn',
        error_details: error.response?.data || null
      };
    }
  }
});

/**
 * Tool trả lời comment trên bài đăng Facebook
 */
export const replyToCommentTool = createTool({
  id: 'reply_to_comment',
  description: 'Trả lời comment trên bài đăng Facebook',
  inputSchema: z.object({
    comment_id: z.string().describe('ID của comment cần trả lời'),
    message: z.string().describe('Nội dung tin nhắn cần gửi'),
    page_access_token: z.string().optional().describe('Access token của Page (nếu không cung cấp sẽ sử dụng token từ biến môi trường)'),
  }),
  execute: async ({ context }) => {
    try {
      console.log('Đang trả lời comment:', context.comment_id);

      // Sử dụng token từ tham số hoặc biến môi trường
      const pageAccessToken = context.page_access_token || process.env.FACEBOOK_PAGE_ACCESS_TOKEN;

      if (!pageAccessToken) {
        throw new Error('Không tìm thấy Page Access Token. Vui lòng cung cấp token hoặc thiết lập biến môi trường FACEBOOK_PAGE_ACCESS_TOKEN');
      }

      // Trả lời comment
      const response = await axios({
        method: 'POST',
        url: `https://graph.facebook.com/v18.0/${context.comment_id}/comments`,
        params: {
          message: context.message,
          access_token: pageAccessToken
        }
      });

      return {
        success: true,
        comment_id: response.data?.id || null,
        response_data: response.data
      };
    } catch (error: any) {
      console.error('Lỗi khi trả lời comment:', error);

      return {
        success: false,
        error: error.message || 'Lỗi không xác định khi trả lời comment',
        error_details: error.response?.data || null
      };
    }
  }
});

/**
 * Tool lấy danh sách comment mới nhất từ bài đăng
 */
export const getLatestCommentsTool = createTool({
  id: 'get_latest_comments',
  description: 'Lấy danh sách comment mới nhất từ bài đăng Facebook',
  inputSchema: z.object({
    post_id: z.string().describe('ID của bài đăng cần lấy comment'),
    limit: z.number().optional().default(10).describe('Số lượng comment cần lấy'),
    page_access_token: z.string().optional().describe('Access token của Page (nếu không cung cấp sẽ sử dụng token từ biến môi trường)'),
  }),
  execute: async ({ context }) => {
    try {
      console.log('Đang lấy comment mới nhất từ bài đăng:', context.post_id);

      // Sử dụng token từ tham số hoặc biến môi trường
      const pageAccessToken = context.page_access_token || process.env.FACEBOOK_PAGE_ACCESS_TOKEN;

      if (!pageAccessToken) {
        throw new Error('Không tìm thấy Page Access Token. Vui lòng cung cấp token hoặc thiết lập biến môi trường FACEBOOK_PAGE_ACCESS_TOKEN');
      }

      // Lấy danh sách comment
      const response = await axios({
        method: 'GET',
        url: `https://graph.facebook.com/v18.0/${context.post_id}/comments`,
        params: {
          limit: context.limit,
          order: 'reverse_chronological',
          fields: 'id,message,created_time,from',
          access_token: pageAccessToken
        }
      });

      return {
        success: true,
        comments: response.data?.data || [],
        paging: response.data?.paging || null
      };
    } catch (error: any) {
      console.error('Lỗi khi lấy danh sách comment:', error);

      return {
        success: false,
        error: error.message || 'Lỗi không xác định khi lấy danh sách comment',
        error_details: error.response?.data || null
      };
    }
  }
});
