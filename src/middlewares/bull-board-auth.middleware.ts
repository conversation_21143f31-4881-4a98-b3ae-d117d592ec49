/**
 * Authentication Middleware cho Bull Board Dashboard
 * Bảo vệ dashboard khỏi truy cập trái phép
 */
import { Request, Response, NextFunction } from 'express';
import dotenv from 'dotenv';

dotenv.config();

// Cấu hình authentication
const BULL_BOARD_USERNAME = process.env.BULL_BOARD_USERNAME || 'admin';
const BULL_BOARD_PASSWORD = process.env.BULL_BOARD_PASSWORD || 'mooly123';
const BULL_BOARD_ENABLED = process.env.BULL_BOARD_ENABLED !== 'false'; // Default: enabled

/**
 * Basic Authentication middleware
 */
export const bullBoardAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Kiểm tra xem Bull Board có được bật không
  if (!BULL_BOARD_ENABLED) {
    return res.status(404).json({
      error: 'Bull Board dashboard is disabled',
      message: 'Set BULL_BOARD_ENABLED=true in environment variables to enable'
    });
  }

  // Lấy Authorization header
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    // Yêu cầu authentication
    res.setHeader('WWW-Authenticate', 'Basic realm="Bull Board Dashboard"');
    return res.status(401).json({
      error: 'Authentication required',
      message: 'Please provide valid credentials to access Bull Board dashboard'
    });
  }

  try {
    // Decode Basic Auth
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    // Kiểm tra credentials
    if (username === BULL_BOARD_USERNAME && password === BULL_BOARD_PASSWORD) {
      // Authentication thành công - chỉ log khi debug
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Bull Board access granted for user: ${username}`);
      }
      return next();
    } else {
      // Sai credentials
      console.warn(`⚠️ Bull Board authentication failed for user: ${username}`);
      res.setHeader('WWW-Authenticate', 'Basic realm="Bull Board Dashboard"');
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Username or password is incorrect'
      });
    }
  } catch (error) {
    console.error('❌ Bull Board authentication error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'An error occurred during authentication'
    });
  }
};

/**
 * IP Whitelist middleware (optional)
 */
export const bullBoardIPWhitelistMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const allowedIPs = process.env.BULL_BOARD_ALLOWED_IPS?.split(',') || [];
  
  if (allowedIPs.length === 0) {
    // Không có whitelist, cho phép tất cả
    return next();
  }

  const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] as string;
  
  if (allowedIPs.includes(clientIP)) {
    // Chỉ log khi debug
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Bull Board IP whitelist passed for: ${clientIP}`);
    }
    return next();
  } else {
    console.warn(`⚠️ Bull Board access denied for IP: ${clientIP}`);
    return res.status(403).json({
      error: 'Access denied',
      message: 'Your IP address is not allowed to access this dashboard'
    });
  }
};

/**
 * Combined middleware cho Bull Board
 */
export const bullBoardProtectionMiddleware = [
  bullBoardIPWhitelistMiddleware,
  bullBoardAuthMiddleware,
];

/**
 * Development-only middleware (bypass authentication in dev)
 */
export const bullBoardDevMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const bypassAuth = process.env.BULL_BOARD_BYPASS_AUTH === 'true';
  
  if (isDevelopment && bypassAuth) {
    console.log('🔓 Bull Board authentication bypassed (development mode)');
    return next();
  }
  
  return bullBoardAuthMiddleware(req, res, next);
}; 