import weaviate from "weaviate-client";
import dotenv from "dotenv";
import { createMockWeaviateClient } from "../mastra/utils/weaviate-mock";

// Đ<PERSON><PERSON> bảo biến môi trường được load
dotenv.config();

// <PERSON>i<PERSON><PERSON> lưu trữ instance của Weaviate client (singleton)
let clientInstance: any = null;
let isConnecting = false;
let connectionPromise: Promise<any> | null = null;

// Tạo client (lazy initialization)
export let client: any = null;

/**
 * Tạo kết nối Weaviate client
 */
async function createWeaviateClient() {
  const host = process.env.WEAVIATE_HOST || "localhost";
  const httpPort = parseInt(process.env.WEAVIATE_HTTP_PORT || "8080");
  const grpcPort = parseInt(process.env.WEAVIATE_GRPC_PORT || "50051");
  const secure = process.env.WEAVIATE_SECURE === "true";
  const apiKey = process.env.WEAVIATE_API_KEY;

  // Kiểm tra API key
  if (!apiKey) {
    console.warn(
      "WEAVIATE_API_KEY không được cấu hình. Kết nối có thể không được xác thực."
    );
  }

  try {
    // Tạo client
    const newClient = await weaviate.connectToCustom({
      httpHost: host,
      httpPort: httpPort,
      grpcHost: host,
      grpcPort: grpcPort,
      httpSecure: secure,
      grpcSecure: secure,
      ...(apiKey && { authCredentials: new weaviate.ApiKey(apiKey) }),

      headers: {
        "X-OpenAI-Api-Key": process.env.OPENAI_API_KEY || "",
        "X-Studio-Api-Key": process.env.GOOGLE_API_KEY || "",
        "X-Goog-Api-Key": process.env.GOOGLE_API_KEY || "",
      },
    });

    console.log(
      `Đã kết nối thành công đến Weaviate server tại ${host}:${httpPort}`
    );
    return newClient;
  } catch (error: any) {
    console.error("Lỗi khi kết nối đến Weaviate:", error);
    throw new Error(
      `Không thể kết nối đến Weaviate: ${error?.message || "Lỗi không xác định"}`
    );
  }
}

/**
 * Lấy Weaviate client (tạo mới nếu cần)
 */
export const getWeaviateClient = async () => {
  try {
    // Nếu đã có instance, trả về luôn
    if (clientInstance) {
      return clientInstance;
    }

    // Nếu đang trong quá trình kết nối, đợi kết nối hoàn tất
    if (isConnecting && connectionPromise) {
      return await connectionPromise;
    }

    // Bắt đầu quá trình kết nối mới
    isConnecting = true;
    connectionPromise = createWeaviateClient();

    try {
      // Lưu kết quả vào biến singleton
      clientInstance = await connectionPromise;
      // Cập nhật biến client để các module khác có thể sử dụng
      client = clientInstance;
      isConnecting = false;
      return clientInstance;
    } catch (connectionError: any) {
      isConnecting = false;
      connectionPromise = null;
      console.error("Lỗi khi kết nối đến Weaviate:", connectionError);

      // Trả về một client giả lập để tránh crash ứng dụng
      console.warn("Sử dụng Weaviate client giả lập để tránh crash ứng dụng");
      const mockClient = createMockWeaviateClient();
      clientInstance = mockClient;
      client = mockClient;
      return mockClient;
    }
  } catch (error: any) {
    isConnecting = false;
    connectionPromise = null;
    console.error("Lỗi khi lấy Weaviate client:", error);

    // Trả về một client giả lập để tránh crash ứng dụng
    console.warn("Sử dụng Weaviate client giả lập để tránh crash ứng dụng");
    const mockClient = createMockWeaviateClient();
    clientInstance = mockClient;
    client = mockClient;
    return mockClient;
  }
};

/**
 * Lấy instance của Weaviate client (singleton pattern)
 * Sử dụng hàm này để đảm bảo chỉ có một kết nối duy nhất trong toàn bộ ứng dụng
 */
export const getWeaviateClientInstance = async () => {
  return await getWeaviateClient();
};



export default {
  getWeaviateClient,
  getWeaviateClientInstance,
};
