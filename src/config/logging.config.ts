/**
 * <PERSON><PERSON><PERSON> hình logging cho hệ thống đồng bộ sản phẩm
 * Sử dụng để kiểm soát mức độ log trong production
 */

export const LogLevel = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
} as const;

export type LogLevelType = typeof LogLevel[keyof typeof LogLevel];

// Cấu hình log level cho production (chỉ hiển thị log quan trọng)
export const CURRENT_LOG_LEVEL: LogLevelType = process.env.NODE_ENV === 'production' 
  ? LogLevel.INFO 
  : LogLevel.DEBUG;

/**
 * Logger wrapper để kiểm soát log level
 */
export class Logger {
  private static shouldLog(level: LogLevelType): boolean {
    return level <= CURRENT_LOG_LEVEL;
  }

  static error(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      console.error(message, ...args);
    }
  }

  static warn(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(message, ...args);
    }
  }

  static info(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.info(message, ...args);
    }
  }

  static debug(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(message, ...args);
    }
  }

  /**
   * Log cho sync operations - luôn hiển thị trong production
   */
  static sync(message: string, ...args: any[]): void {
    console.info(message, ...args);
  }

  /**
   * Log cho kết quả quan trọng - luôn hiển thị
   */
  static result(message: string, ...args: any[]): void {
    console.info(message, ...args);
  }
}

/**
 * Cấu hình log cho từng module
 */
export const LogConfig = {
  // Hiển thị log chi tiết cho images chỉ khi có lỗi
  IMAGES_VERBOSE: process.env.NODE_ENV !== 'production',
  
  // Hiển thị log chi tiết cho Weaviate chỉ khi có lỗi
  WEAVIATE_VERBOSE: process.env.NODE_ENV !== 'production',
  
  // Hiển thị log chi tiết cho product processing chỉ khi có lỗi
  PRODUCT_VERBOSE: process.env.NODE_ENV !== 'production',
  
  // Luôn hiển thị log cho sync operations
  SYNC_VERBOSE: true,
} as const;
