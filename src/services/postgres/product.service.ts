import { sql } from '../../config/postgres';

/**
 * Hàm tiện ích để xử lý và lọc thuộc tính sản phẩm từ trường JSON attributes
 * @param products Danh sách sản phẩm
 * @param attributeFilters C<PERSON>c bộ lọc thuộc tính (color, size, style, etc.)
 * @returns Danh sách sản phẩm đã được lọc
 */
const filterProductsByAttributes = (
  products: any[],
  attributeFilters: { [key: string]: string } = {}
): any[] => {
  // Nếu không có bộ lọc thuộc tính, trả về tất cả sản phẩm
  if (Object.keys(attributeFilters).length === 0) {
    return products;
  }

  console.log(`[filterProductsByAttributes] Bắt đầu lọc ${products.length} sản phẩm với các thuộc tính:`, attributeFilters);

  // <PERSON><PERSON><PERSON> sản phẩm theo thuộc tính
  const filteredProducts = products.filter(product => {
    // Nếu sản phẩm không có thuộc tính, bỏ qua
    if (!product.attributes || !Array.isArray(product.attributes)) {
      console.log(`[filterProductsByAttributes] Sản phẩm ${product.id} không có thuộc tính hoặc không phải mảng`);
      return false;
    }

    // Log thuộc tính của sản phẩm để debug
    console.log(`[filterProductsByAttributes] Sản phẩm ${product.id} có thuộc tính:`, JSON.stringify(product.attributes, null, 2));

    // Kiểm tra từng bộ lọc thuộc tính
    for (const [attrName, attrValue] of Object.entries(attributeFilters)) {
      // Tìm thuộc tính trong mảng attributes
      const attribute = product.attributes.find(
        (attr: any) => attr.name?.toLowerCase() === attrName.toLowerCase()
      );

      // Nếu không tìm thấy thuộc tính hoặc giá trị không khớp, loại bỏ sản phẩm
      if (!attribute || !attribute.values || !Array.isArray(attribute.values)) {
        console.log(`[filterProductsByAttributes] Sản phẩm ${product.id} không có thuộc tính ${attrName}`);
        return false;
      }

      // Kiểm tra xem giá trị thuộc tính có chứa giá trị cần lọc không (không phân biệt hoa thường)
      const hasMatchingValue = attribute.values.some(
        (value: string) => value.toLowerCase().includes(attrValue.toLowerCase())
      );

      if (!hasMatchingValue) {
        console.log(`[filterProductsByAttributes] Sản phẩm ${product.id} không có giá trị ${attrValue} cho thuộc tính ${attrName}`);
        return false;
      }
    }

    // Sản phẩm đã vượt qua tất cả các bộ lọc
    console.log(`[filterProductsByAttributes] Sản phẩm ${product.id} phù hợp với tất cả các bộ lọc`);
    return true;
  });

  console.log(`[filterProductsByAttributes] Kết quả: ${filteredProducts.length}/${products.length} sản phẩm phù hợp`);
  return filteredProducts;
};

/**
 * Lấy thông tin chi tiết của nhiều sản phẩm theo danh sách ID
 * Tối ưu hóa để lấy thông tin cần thiết: tên, giá, biến thể, tồn kho, mô tả
 * @param productIds Danh sách ID sản phẩm cần lấy thông tin
 * @param tenant_id ID của tenant
 * @param bot_id ID của bot (để lọc sản phẩm theo quyền truy cập)
 */
export const getProductDetailsByIds = async ({
  productIds,
  tenant_id,
  bot_id,
}: {
  productIds: string[];
  tenant_id: string;
  bot_id?: string;
}) => {
  try {

    if (!productIds.length) {
      return {
        success: true,
        data: [],
        total: 0,
      };
    }

    // Thực hiện truy vấn SQL trực tiếp - chỉ lấy các field cần thiết cho AI
    const products = await sql`
      SELECT
        id,
        name,
        price,
        sale_price,
        stock_quantity,
        description,
        short_description,
        avatar,
        images,
        type,
        sku,
        is_active
      FROM products
      WHERE id IN ${sql(productIds)}
      AND tenant_id = ${tenant_id}
    `;


    if (products?.length === 0) {

      // Thử truy vấn từng ID riêng lẻ để xem có vấn đề gì không
      const individualResults = [];

      for (const productId of productIds) {
        const singleProduct = await sql`
          SELECT id, name FROM products
          WHERE id = ${productId}
        `;

        if (singleProduct && singleProduct.length > 0) {
          individualResults.push(singleProduct[0]);
        } else {
          console.log(`[getProductDetailsByIds] ID ${productId}: Không tìm thấy`);
        }
      }


      // Nếu tìm thấy sản phẩm từ truy vấn riêng lẻ
      if (individualResults.length > 0) {
        return {
          success: true,
          data: individualResults,
          total: individualResults.length,
        };
      }

      return {
        success: true,
        data: [],
        total: 0,
      };
    }

    // Lấy thông tin biến thể cho các sản phẩm có biến thể
    const productsWithVariableType = products?.filter(product => product.type === 'variable') || [];
    const productIdsWithVariableType = productsWithVariableType.map(product => product.id);

    let variants: any[] = [];
    if (productIdsWithVariableType.length > 0) {
      variants = await sql`
        SELECT
          id,
          product_id,
          name,
          price,
          stock_quantity,
          sku,
          attributes,
          is_active
        FROM product_variants
        WHERE tenant_id = ${tenant_id}
        AND product_id IN ${sql(productIdsWithVariableType)}
      `;
    }

    // Kết hợp sản phẩm với biến thể tương ứng
    const productsWithVariants = products?.map(product => {
      const productVariants = variants.filter(variant => variant.product_id === product.id);
      return {
        ...product,
        variants: productVariants.length > 0 ? productVariants : undefined
      };
    }) || [];

    return {
      success: true,
      data: productsWithVariants,
      total: productsWithVariants.length,
    };
  } catch (error: any) {
    console.error('[getProductDetailsByIds] Lỗi không xác định:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin chi tiết sản phẩm: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tìm kiếm sản phẩm theo SKU
 * @param sku Mã SKU của sản phẩm
 * @param tenant_id ID của tenant
 */
export const getProductBySku = async ({
  sku,
  tenant_id,
}: {
  sku: string;
  tenant_id: string;
}) => {
  try {
    console.log(`[getProductBySku] Tìm kiếm sản phẩm với SKU: ${sku}, tenant_id: ${tenant_id}`);

    // Tìm kiếm trong bảng products
    const product = await sql`
      SELECT p.*,
        json_agg(
          json_build_object(
            'id', pc.id,
            'name', pc.name,
            'slug', pc.slug
          )
        ) as product_categories
      FROM products p
      LEFT JOIN product_categories pc ON p.category_id = pc.id
      WHERE p.sku = ${sku}
      AND p.tenant_id = ${tenant_id}
      GROUP BY p.id
    `;

    console.log(`[getProductBySku] Kết quả tìm kiếm trong bảng products:`, product.length > 0 ? 'Tìm thấy' : 'Không tìm thấy');

    // Nếu tìm thấy sản phẩm, trả về kết quả
    if (product && product.length > 0) {
      // Lấy thông tin biến thể nếu có (type = 'variable')
      let variants = null;
      if (product[0].type === 'variable') {
        variants = await sql`
          SELECT * FROM product_variants
          WHERE product_id = ${product[0].id}
          AND tenant_id = ${tenant_id}
        `;
      }

      return {
        success: true,
        data: [{ ...product[0], variants: variants || [] }],
        total: 1,
      };
    }

    console.log(`[getProductBySku] Không tìm thấy trong bảng products, tìm trong bảng product_variants`);

    // Nếu không tìm thấy trong bảng products, tìm trong bảng product_variants
    const variant = await sql`
      SELECT v.*,
        (
          SELECT json_build_object(
            'id', p.id,
            'name', p.name,
            'description', p.description,
            'price', p.price,
            'avatar', p.avatar,
            'product_categories', (
              SELECT json_agg(
                json_build_object(
                  'id', pc.id,
                  'name', pc.name,
                  'slug', pc.slug
                )
              )
              FROM product_categories pc
              WHERE p.category_id = pc.id
            )
          )
          FROM products p
          WHERE p.id = v.product_id
        ) as products
      FROM product_variants v
      WHERE v.sku = ${sku}
      AND v.tenant_id = ${tenant_id}
    `;

    console.log(`[getProductBySku] Kết quả tìm kiếm trong bảng product_variants:`, variant.length > 0 ? 'Tìm thấy' : 'Không tìm thấy');

    // Nếu tìm thấy biến thể, trả về sản phẩm chính với biến thể đó
    if (variant && variant.length > 0 && variant[0].products) {
      const productWithVariant = {
        ...variant[0].products,
        variants: [variant[0]],
      };

      return {
        success: true,
        data: [productWithVariant],
        total: 1,
      };
    }

    // Không tìm thấy sản phẩm hoặc biến thể nào với SKU đã cho
    return {
      success: true,
      data: [],
      total: 0,
    };
  } catch (error: any) {
    console.error(`Lỗi khi tìm kiếm sản phẩm với SKU ${sku}:`, error);
    return {
      success: false,
      message: `Lỗi khi tìm kiếm sản phẩm với SKU ${sku}: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy danh sách sản phẩm từ PostgreSQL với các bộ lọc
 * @param filters Các bộ lọc để tìm kiếm sản phẩm
 * @param tenant_id ID của tenant
 * @param limit Số lượng sản phẩm tối đa trả về
 * @param offset Vị trí bắt đầu lấy dữ liệu
 */
export const getProducts = async ({
  filters = {},
  tenant_id,
  limit = 10,
  offset = 0,
}: {
  filters?: {
    query?: string;
    category_id?: string;
    category?: string;
    color?: string;
    size?: string;
    style?: string;
    price_range?: { min?: number; max?: number };
    is_active?: boolean;
  };
  tenant_id: string;
  limit?: number;
  offset?: number;
}) => {
  try {
    console.log(`[getProducts] Tìm kiếm sản phẩm với filters:`, JSON.stringify(filters, null, 2));
    console.log(`[getProducts] tenant_id: ${tenant_id}, limit: ${limit}, offset: ${offset}`);

    // Xây dựng truy vấn SQL động
    let queryConditions = [];
    let queryParams: any[] = [tenant_id];

    // Điều kiện cơ bản
    queryConditions.push(`tenant_id = $1`);

    // Áp dụng các bộ lọc
    if (filters.query) {
      queryConditions.push(`(name ILIKE $${queryParams.length + 1} OR description ILIKE $${queryParams.length + 1})`);
      queryParams.push(`%${filters.query}%`);
    }

    if (filters.category_id) {
      queryConditions.push(`category_id = $${queryParams.length + 1}`);
      queryParams.push(filters.category_id);
    }

    if (filters.price_range) {
      if (filters.price_range.min !== undefined) {
        queryConditions.push(`price >= $${queryParams.length + 1}`);
        queryParams.push(filters.price_range.min);
      }
      if (filters.price_range.max !== undefined) {
        queryConditions.push(`price <= $${queryParams.length + 1}`);
        queryParams.push(filters.price_range.max);
      }
    }

    if (filters.is_active !== undefined) {
      queryConditions.push(`is_active = $${queryParams.length + 1}`);
      queryParams.push(filters.is_active);
    }

    // Xây dựng câu truy vấn SQL hoàn chỉnh
    const whereClause = queryConditions.length > 0 ? `WHERE ${queryConditions.join(' AND ')}` : '';

    // Thực hiện truy vấn SQL
    const query = `
      SELECT p.*,
        (
          SELECT json_agg(
            json_build_object(
              'id', pc.id,
              'name', pc.name,
              'slug', pc.slug
            )
          )
          FROM product_categories pc
          WHERE p.category_id = pc.id
        ) as product_categories
      FROM products p
      ${whereClause}
      ORDER BY p.created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    console.log(`[getProducts] Thực thi truy vấn SQL:`, query);
    console.log(`[getProducts] Với tham số:`, queryParams);

    // Thực hiện truy vấn với postgres
    const data = await sql.unsafe(query, queryParams);

    console.log(`[getProducts] Tìm thấy ${data?.length || 0} sản phẩm từ truy vấn trực tiếp`);

    // Lọc sản phẩm theo thuộc tính (color, size, style) nếu có
    const attributeFilters: { [key: string]: string } = {};
    if (filters.color) attributeFilters.color = filters.color;
    if (filters.size) attributeFilters.size = filters.size;
    if (filters.style) attributeFilters.style = filters.style;

    console.log(`[getProducts] Áp dụng bộ lọc thuộc tính:`, JSON.stringify(attributeFilters, null, 2));

    // Áp dụng bộ lọc thuộc tính
    const filteredData = Object.keys(attributeFilters).length > 0
      ? filterProductsByAttributes(data || [], attributeFilters)
      : data || [];

    console.log(`[getProducts] Sau khi lọc thuộc tính: ${filteredData.length}/${data?.length || 0} sản phẩm`);

    return {
      success: true,
      data: filteredData,
      total: filteredData.length,
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách sản phẩm:', error);
    return {
      success: false,
      message: `Lỗi khi lấy danh sách sản phẩm: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy thông tin chi tiết của một sản phẩm
 * @param product_id ID của sản phẩm
 * @param sku Mã SKU của sản phẩm
 * @param tenant_id ID của tenant
 */
export const getProductDetails = async ({
  product_id,
  sku,
  tenant_id,
}: {
  product_id: string;
  sku: string;
  tenant_id: string;
}) => {
  try {

    // Kiểm tra xem có cung cấp product_id hoặc sku không
    if (!product_id && !sku) {
      return {
        success: false,
        message: 'Vui lòng cung cấp product_id hoặc sku để tìm kiếm sản phẩm',
      };
    }

    // Sử dụng tham số được chuẩn bị thay vì nhúng trực tiếp giá trị
    let query = '';
    let params: any = [];

    if (product_id && sku) {
      query = `
        SELECT p.*,
          (
            SELECT json_agg(
              json_build_object(
                'id', pc.id,
                'name', pc.name,
                'slug', pc.slug
              )
            )
            FROM product_categories pc
            WHERE p.category_id = pc.id
          ) as product_categories
        FROM products p
        WHERE (p.id = $1 OR p.sku = $2) AND p.tenant_id = $3
      `;
      params = [product_id, sku, tenant_id];
    } else if (product_id) {
      query = `
        SELECT p.*,
          (
            SELECT json_agg(
              json_build_object(
                'id', pc.id,
                'name', pc.name,
                'slug', pc.slug
              )
            )
            FROM product_categories pc
            WHERE p.category_id = pc.id
          ) as product_categories
        FROM products p
        WHERE p.id = $1 AND p.tenant_id = $2
      `;
      params = [product_id, tenant_id];
    } else if (sku) {
      query = `
        SELECT p.*,
          (
            SELECT json_agg(
              json_build_object(
                'id', pc.id,
                'name', pc.name,
                'slug', pc.slug
              )
            )
            FROM product_categories pc
            WHERE p.category_id = pc.id
          ) as product_categories
        FROM products p
        WHERE p.sku = $1 AND p.tenant_id = $2
      `;
      params = [sku, tenant_id];
    }

    const products = await sql.unsafe(query, params);

    // Nếu không tìm thấy trong bảng products và có sku, tìm trong bảng product_variants
    if ((!products || products.length === 0) && sku) {

      // Tìm kiếm biến thể theo SKU sử dụng tham số được chuẩn bị
      const variantQuery = `
        SELECT v.*,
          (
            SELECT json_build_object(
              'id', p.id,
              'name', p.name,
              'description', p.description,
              'price', p.price,
              'avatar', p.avatar,
              'product_categories', (
                SELECT json_agg(
                  json_build_object(
                    'id', pc.id,
                    'name', pc.name,
                    'slug', pc.slug
                  )
                )
                FROM product_categories pc
                WHERE p.category_id = pc.id
              )
            )
            FROM products p
            WHERE p.id = v.product_id
          ) as product
        FROM product_variants v
        WHERE v.sku = $1
        AND v.tenant_id = $2
      `;

      const variants = await sql.unsafe(variantQuery, [sku, tenant_id]);

      if (variants && variants.length > 0 && variants[0].product) {

        // Lấy thông tin sản phẩm từ biến thể
        const productFromVariant = variants[0].product;

        return {
          success: true,
          data: {
            ...productFromVariant,
            variants: [variants[0]],
          },
        };
      }

      // Không tìm thấy sản phẩm hoặc biến thể nào
      return {
        success: false,
        message: `Không tìm thấy sản phẩm hoặc biến thể nào với SKU: ${sku}`,
      };
    }

    // Nếu không tìm thấy sản phẩm
    if (!products || products.length === 0) {
      const errorMessage = product_id
        ? `Không tìm thấy sản phẩm với mã ${product_id}`
        : `Không tìm thấy sản phẩm với SKU ${sku}`;

      console.error(`[getProductDetails] ${errorMessage}`);
      return {
        success: false,
        message: errorMessage,
      };
    }

    const product = products[0];

    // Lấy thông tin biến thể nếu có (type = 'variable')
    let variants = null;
    if (product.type === 'variable') {

      // Sử dụng tham số được chuẩn bị cho truy vấn biến thể
      const variantsQuery = `
        SELECT * FROM product_variants
        WHERE product_id = $1
        AND tenant_id = $2
      `;

      variants = await sql.unsafe(variantsQuery, [product.id, tenant_id]);
    }

    return {
      success: true,
      data: {
        ...product,
        variants: variants || [],
      },
    };
  } catch (error: any) {
    console.error('[getProductDetails] Lỗi khi lấy thông tin chi tiết sản phẩm:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin chi tiết sản phẩm: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Kiểm tra tình trạng còn hàng của sản phẩm
 * @param product_id ID của sản phẩm
 * @param variant_id ID của biến thể (nếu có)
 * @param tenant_id ID của tenant
 */
export const checkProductAvailability = async ({
  product_id,
  variant_id,
  tenant_id,
}: {
  product_id: string;
  variant_id?: string;
  tenant_id: string;
}) => {
  try {
    if (variant_id) {
      // Kiểm tra tình trạng còn hàng của biến thể
      const variants = await sql`
        SELECT stock_quantity, sku, price
        FROM product_variants
        WHERE id = ${variant_id}
        AND product_id = ${product_id}
        AND tenant_id = ${tenant_id}
      `;

      if (!variants || variants.length === 0) {
        console.error(`Không tìm thấy biến thể với mã ${variant_id}`);
        return {
          success: false,
          message: `Không tìm thấy biến thể với mã ${variant_id}`,
        };
      }

      const variant = variants[0];
      return {
        success: true,
        data: {
          in_stock: variant.stock_quantity > 0,
          quantity: variant.stock_quantity,
          sku: variant.sku,
          price: variant.price,
        },
      };
    } else {
      // Kiểm tra tình trạng còn hàng của sản phẩm
      const products = await sql`
        SELECT stock_quantity, sku, price, type
        FROM products
        WHERE id = ${product_id}
        AND tenant_id = ${tenant_id}
      `;

      if (!products || products.length === 0) {
        console.error(`Không tìm thấy sản phẩm với mã ${product_id}`);
        return {
          success: false,
          message: `Không tìm thấy sản phẩm với mã ${product_id}`,
        };
      }

      const product = products[0];

      // Nếu sản phẩm có biến thể nhưng không cung cấp variant_id
      if (product.type === 'variable') {
        return {
          success: false,
          message: 'Sản phẩm này có nhiều biến thể, vui lòng chọn biến thể cụ thể',
        };
      }

      return {
        success: true,
        data: {
          in_stock: product.stock_quantity > 0,
          quantity: product.stock_quantity,
          sku: product.sku,
          price: product.price,
        },
      };
    }
  } catch (error: any) {
    console.error('Lỗi khi kiểm tra tình trạng còn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi kiểm tra tình trạng còn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy danh sách danh mục sản phẩm
 * @param tenant_id ID của tenant
 */
export const getProductCategories = async ({ tenant_id }: { tenant_id: string }) => {
  try {
    const data = await sql`
      SELECT id, name, slug, description, image_url, parent_id
      FROM product_categories
      WHERE tenant_id = ${tenant_id}
      AND is_active = true
      ORDER BY sort_order ASC
    `;

    return {
      success: true,
      data: data || [],
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy danh sách danh mục sản phẩm:', error);
    return {
      success: false,
      message: `Lỗi khi lấy danh sách danh mục sản phẩm: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy thông tin đơn hàng theo order_code (order_number)
 * @param order_code Mã đơn hàng
 * @param tenant_id ID của tenant
 */
export const getOrderByCode = async ({
  order_code,
  tenant_id,
}: {
  order_code: string;
  tenant_id: string;
}) => {
  try {
    console.log(`[getOrderByCode] Lấy thông tin đơn hàng với mã: ${order_code}, tenant_id: ${tenant_id}`);

    // Lấy thông tin đơn hàng
    const orders = await sql`
      SELECT o.*,
        (
          SELECT json_build_object(
            'id', ca.id,
            'full_name', ca.full_name,
            'phone', ca.phone,
            'address', ca.address,
            'province', ca.province,
            'district', ca.district,
            'ward', ca.ward,
            'country', ca.country
          )
          FROM customer_addresses ca
          WHERE ca.id = o.shipping_address_id
        ) as shipping_address
      FROM orders o
      WHERE o.order_number = ${order_code}
      AND o.tenant_id = ${tenant_id}
    `;

    if (!orders || orders.length === 0) {
      console.error(`Không tìm thấy đơn hàng với mã ${order_code}`);
      return {
        success: false,
        message: `Không tìm thấy đơn hàng với mã ${order_code}`,
      };
    }

    const order = orders[0];

    // Lấy thông tin các sản phẩm trong đơn hàng
    const orderItems = await sql`
      SELECT *
      FROM order_items
      WHERE order_id = ${order.id}
      AND tenant_id = ${tenant_id}
    `;

    // Lấy lịch sử đơn hàng chỉ sử dụng status enum
    const orderHistory = await sql`
      SELECT
        oh.id,
        oh.tenant_id,
        oh.order_id,
        oh.created_at,
        oh.comment,
        oh.status
      FROM order_history oh
      WHERE oh.order_id = ${order.id}
      AND oh.tenant_id = ${tenant_id}
      ORDER BY oh.created_at DESC
    `;

    return {
      success: true,
      data: {
        ...order,
        items: orderItems || [],
        history: orderHistory || [],
      },
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tạo đơn hàng mới
 * @param orderData Thông tin đơn hàng
 * @param tenant_id ID của tenant
 */
export const createOrder = async ({
  orderData,
  tenant_id,
}: {
  orderData: {
    customer_id?: string;
    customer_info: {
      name: string;
      phone: string;
    };
    shipping_address: {
      full_name: string;
      phone: string;
      address: string;
      province?: string;
      district?: string;
      ward?: string;
      country?: string;
    };
    items: Array<{
      product_id: string;
      variant_id?: string;
      quantity: number;
      unit_price: number;
    }>;
    payment_method: string;
    shipping_method?: string;
    shipping_fee?: number;
    promotion_code?: string;
    notes?: string;
    chatbot_info?: {
      resource_id?: string;
      thread_id?: string;
      bot_id?: string;
      created_at?: string;
    };
  };
  tenant_id: string;
}) => {
  try {
    // Tạo mã đơn hàng duy nhất
    const timestamp = Date.now();
    const randomChars = Math.random().toString(36).substring(2, 6).toUpperCase();
    const orderNumber = `ORD-${timestamp}-${randomChars}`;

    console.log(`[createOrder] Tạo đơn hàng mới với mã: ${orderNumber}`);

    // Sử dụng sql.begin() để bắt đầu transaction đúng cách
    return await sql.begin(async (transaction) => {
      try {
        // Tạo đơn hàng với status là enum 'pending'
        const order = await transaction`
          INSERT INTO orders (
            tenant_id,
            customer_id,
            customer_name,
            customer_phone,
            payment_method,
            shipping_method,
            notes,
            status,
            order_number,
            chatbot_info
          ) VALUES (
            ${tenant_id},
            ${orderData.customer_id || null},
            ${orderData.customer_info.name},
            ${orderData.customer_info.phone},
            ${orderData.payment_method},
            ${orderData.shipping_method || null},
            ${orderData.notes || null},
            'pending'::order_status_enum,
            ${orderNumber},
            ${orderData.chatbot_info ? sql.json(orderData.chatbot_info) : null}
          )
          RETURNING *
        `;

        if (!order || order.length === 0) {
          throw new Error('Không thể tạo đơn hàng');
        }

        // Tạo địa chỉ giao hàng
        const address = await transaction`
          INSERT INTO customer_addresses (
            tenant_id,
            customer_id,
            full_name,
            phone,
            address,
            province,
            district,
            ward,
            country
          ) VALUES (
            ${tenant_id},
            ${orderData.customer_id || null},
            ${orderData.shipping_address.full_name},
            ${orderData.shipping_address.phone},
            ${orderData.shipping_address.address},
            ${orderData.shipping_address.province || null},
            ${orderData.shipping_address.district || null},
            ${orderData.shipping_address.ward || null},
            ${orderData.shipping_address.country || 'Vietnam'}
          )
          RETURNING *
        `;

        if (!address || address.length === 0) {
          throw new Error('Không thể tạo địa chỉ giao hàng');
        }

        // Cập nhật đơn hàng với địa chỉ giao hàng
        await transaction`
          UPDATE orders
          SET
            shipping_address_id = ${address[0].id},
            billing_address_id = ${address[0].id}
          WHERE id = ${order[0].id}
        `;

        // Thêm các sản phẩm vào đơn hàng
        const orderItems = [];
        let subtotal = 0;
        let productIds = [];
        let categoryIds = [];

        for (const item of orderData.items) {
          // Lấy thông tin sản phẩm
          let productInfo: any = {};

          if (item.variant_id) {
            const variant = await transaction`
              SELECT v.*, p.name, p.avatar, p.category_id
              FROM product_variants v
              JOIN products p ON v.product_id = p.id
              WHERE v.id = ${item.variant_id}
              AND v.product_id = ${item.product_id}
            `;

            if (!variant || variant.length === 0) {
              console.error(`Không tìm thấy biến thể với mã ${item.variant_id}`);
              continue;
            }

            productInfo = {
              name: variant[0].name,
              image_url: variant[0].avatar || variant[0].products?.avatar,
              variant_info: {
                sku: variant[0].sku,
                attributes: variant[0].attributes,
              },
              category_id: variant[0].category_id
            };

            // Thêm product_id và category_id vào danh sách để kiểm tra khuyến mãi
            productIds.push(item.product_id);
            if (variant[0].category_id) {
              categoryIds.push(variant[0].category_id);
            }
          } else {
            const product = await transaction`
              SELECT name, avatar, category_id
              FROM products
              WHERE id = ${item.product_id}
            `;

            if (!product || product.length === 0) {
              console.error(`Không tìm thấy sản phẩm với mã ${item.product_id}`);
              continue;
            }

            productInfo = {
              name: product[0].name,
              image_url: product[0].avatar,
              category_id: product[0].category_id
            };

            // Thêm product_id và category_id vào danh sách để kiểm tra khuyến mãi
            productIds.push(item.product_id);
            if (product[0].category_id) {
              categoryIds.push(product[0].category_id);
            }
          }

          // Tính tổng giá
          const totalPrice = item.unit_price * item.quantity;
          subtotal += totalPrice;

          // Thêm sản phẩm vào đơn hàng
          const orderItem = await transaction`
            INSERT INTO order_items (
              tenant_id,
              order_id,
              product_id,
              variant_id,
              name,
              variant_info,
              quantity,
              unit_price,
              total_price
            ) VALUES (
              ${tenant_id},
              ${order[0].id},
              ${item.product_id},
              ${item.variant_id || null},
              ${productInfo.name},
              ${productInfo.variant_info ? sql.json(productInfo.variant_info) : null},
              ${item.quantity},
              ${item.unit_price},
              ${totalPrice}
            )
            RETURNING *
          `;

          orderItems.push(orderItem[0]);
        }

        // Tính phí vận chuyển
        let shippingAmount = 0;
        if (orderData.shipping_fee !== undefined) {
          // Sử dụng phí vận chuyển được cung cấp
          shippingAmount = orderData.shipping_fee;
        } else if (orderData.shipping_method) {
          // Lấy phí vận chuyển từ phương thức vận chuyển
          const shippingMethod = await transaction`
            SELECT * FROM shipping_methods
            WHERE id = ${orderData.shipping_method}
            AND tenant_id = ${tenant_id}
            AND is_active = true
          `;

          if (shippingMethod && shippingMethod.length > 0) {
            shippingAmount = parseFloat(shippingMethod[0].price) || 0;

            // Kiểm tra ngưỡng miễn phí vận chuyển
            if (shippingMethod[0].free_shipping_threshold && subtotal >= parseFloat(shippingMethod[0].free_shipping_threshold)) {
              shippingAmount = 0;
            }
          }
        } else {
          // Lấy phí vận chuyển mặc định từ bảng shipping_fees
          const shippingFees = await transaction`
            SELECT * FROM shipping_fees
            WHERE tenant_id = ${tenant_id}
            AND is_active = true
            AND type = 'fixed'
            ORDER BY sort_order ASC
            LIMIT 1
          `;

          if (shippingFees && shippingFees.length > 0) {
            shippingAmount = parseFloat(shippingFees[0].amount) || 0;

            // Kiểm tra ngưỡng đơn hàng tối thiểu
            if (shippingFees[0].min_order_amount && subtotal < parseFloat(shippingFees[0].min_order_amount)) {
              shippingAmount = parseFloat(shippingFees[0].amount) || 0;
            }

            // Kiểm tra ngưỡng đơn hàng tối đa
            if (shippingFees[0].max_order_amount && subtotal >= parseFloat(shippingFees[0].max_order_amount)) {
              shippingAmount = 0;
            }
          }
        }

        // Tính khuyến mãi
        let discountAmount = 0;
        let appliedPromotionId = null;

        if (orderData.promotion_code) {
          // Lấy thông tin khuyến mãi từ mã khuyến mãi
          const promotion = await transaction`
            SELECT * FROM promotions
            WHERE code = ${orderData.promotion_code}
            AND tenant_id = ${tenant_id}
            AND is_active = true
            AND (start_date IS NULL OR start_date <= NOW())
            AND (end_date IS NULL OR end_date >= NOW())
          `;

          if (promotion && promotion.length > 0) {
            const promo = promotion[0];

            // Kiểm tra điều kiện áp dụng khuyến mãi
            let canApply = true;

            // Kiểm tra giá trị đơn hàng tối thiểu
            if (promo.min_purchase_amount && subtotal < parseFloat(promo.min_purchase_amount)) {
              canApply = false;
              console.log(`[createOrder] Đơn hàng không đạt giá trị tối thiểu ${promo.min_purchase_amount} để áp dụng khuyến mãi`);
            }

            // Kiểm tra giới hạn sử dụng
            if (promo.usage_limit && promo.usage_count >= promo.usage_limit) {
              canApply = false;
              console.log(`[createOrder] Khuyến mãi đã đạt giới hạn sử dụng ${promo.usage_limit} lần`);
            }

            // Kiểm tra phạm vi áp dụng
            if (promo.applies_to === 'products' && promo.eligible_product_ids) {
              const eligibleProductIds = Array.isArray(promo.eligible_product_ids)
                ? promo.eligible_product_ids
                : JSON.parse(promo.eligible_product_ids);

              // Kiểm tra xem có sản phẩm nào trong đơn hàng thuộc danh sách sản phẩm được áp dụng không
              const hasEligibleProduct = productIds.some(id => eligibleProductIds.includes(id));
              if (!hasEligibleProduct) {
                canApply = false;
                console.log(`[createOrder] Đơn hàng không có sản phẩm nào thuộc danh sách được áp dụng khuyến mãi`);
              }
            } else if (promo.applies_to === 'categories' && promo.eligible_category_ids) {
              const eligibleCategoryIds = Array.isArray(promo.eligible_category_ids)
                ? promo.eligible_category_ids
                : JSON.parse(promo.eligible_category_ids);

              // Kiểm tra xem có sản phẩm nào trong đơn hàng thuộc danh mục được áp dụng không
              const hasEligibleCategory = categoryIds.some(id => eligibleCategoryIds.includes(id));
              if (!hasEligibleCategory) {
                canApply = false;
                console.log(`[createOrder] Đơn hàng không có sản phẩm nào thuộc danh mục được áp dụng khuyến mãi`);
              }
            }

            // Tính giá trị khuyến mãi nếu có thể áp dụng
            if (canApply) {
              if (promo.value_type === 'percentage') {
                discountAmount = (subtotal * parseFloat(promo.value)) / 100;
              } else if (promo.value_type === 'fixed') {
                discountAmount = parseFloat(promo.value);
              }

              // Kiểm tra giá trị khuyến mãi tối đa
              if (promo.max_discount_amount && discountAmount > parseFloat(promo.max_discount_amount)) {
                discountAmount = parseFloat(promo.max_discount_amount);
              }

              // Cập nhật số lần sử dụng khuyến mãi
              await transaction`
                UPDATE promotions
                SET usage_count = usage_count + 1
                WHERE id = ${promo.id}
              `;

              appliedPromotionId = promo.id;
            }
          }
        }

        // Tính tổng giá trị đơn hàng
        const totalAmount = subtotal + shippingAmount - discountAmount;

        // Cập nhật tổng giá trị đơn hàng
        await transaction`
          UPDATE orders
          SET
            subtotal = ${subtotal},
            shipping_amount = ${shippingAmount},
            discount_amount = ${discountAmount},
            total_amount = ${totalAmount}
          WHERE id = ${order[0].id}
        `;

        // Thêm lịch sử đơn hàng chỉ sử dụng status enum
        // Sử dụng cùng giá trị enum 'pending' như trong bảng orders
        await transaction.unsafe(`
          INSERT INTO order_history (
            tenant_id,
            order_id,
            status,
            comment
          ) VALUES (
            $1,
            $2,
            $3::order_status_enum,
            $4
          )
        `, [tenant_id, order[0].id, 'pending', 'Đơn hàng mới được tạo']);

        // Transaction sẽ tự động commit khi hàm callback hoàn thành mà không có lỗi
        return {
          success: true,
          data: {
            order_id: order[0].id,
            order_number: order[0].order_number,
            subtotal: subtotal,
            shipping_amount: shippingAmount,
            discount_amount: discountAmount,
            total_amount: totalAmount,
            status: 'pending',
            applied_promotion_id: appliedPromotionId
          },
        };
      } catch (error: any) {
        // Transaction sẽ tự động rollback khi có lỗi
        console.error('Lỗi trong transaction:', error);
        throw error;
      }
    });
  } catch (error: any) {
    console.error('Lỗi khi tạo đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi tạo đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Cập nhật trạng thái đơn hàng
 * @param order_id ID của đơn hàng
 * @param status Trạng thái mới của đơn hàng (sử dụng enum order_status_enum)
 * @param comment Ghi chú về việc thay đổi trạng thái
 * @param tenant_id ID của tenant
 */
export const updateOrderStatus = async ({
  order_id,
  status,
  comment,
  tenant_id,
}: {
  order_id: string;
  status: string; // Giá trị enum: 'pending', 'processing', 'shipped', 'delivered', 'cancelled'
  comment: string;
  tenant_id: string;
}) => {
  try {
    console.log(`[updateOrderStatus] Cập nhật trạng thái đơn hàng ${order_id} thành ${status}`);

    // Sử dụng sql.begin() để bắt đầu transaction
    return await sql.begin(async (transaction) => {
      try {
        // Kiểm tra đơn hàng tồn tại
        const orderCheck = await transaction`
          SELECT id, status FROM orders
          WHERE id = ${order_id}
          AND tenant_id = ${tenant_id}
        `;

        if (!orderCheck || orderCheck.length === 0) {
          return {
            success: false,
            message: `Không tìm thấy đơn hàng với ID ${order_id}`,
          };
        }

        // Cập nhật trạng thái đơn hàng
        await transaction`
          UPDATE orders
          SET
            status = ${status}::order_status_enum,
            updated_at = NOW()
          WHERE id = ${order_id}
          AND tenant_id = ${tenant_id}
        `;

        // Thêm lịch sử đơn hàng chỉ sử dụng status enum
        await transaction.unsafe(`
          INSERT INTO order_history (
            tenant_id,
            order_id,
            status,
            comment
          ) VALUES (
            $1,
            $2,
            $3::order_status_enum,
            $4
          )
        `, [tenant_id, order_id, status, comment]);

        return {
          success: true,
          data: {
            order_id,
            status,
            updated_at: new Date().toISOString(),
          },
        };
      } catch (error: any) {
        console.error('Lỗi trong transaction:', error);
        throw error;
      }
    });
  } catch (error: any) {
    console.error('Lỗi khi cập nhật trạng thái đơn hàng:', error);
    return {
      success: false,
      message: `Lỗi khi cập nhật trạng thái đơn hàng: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};