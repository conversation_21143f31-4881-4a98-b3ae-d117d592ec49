import { sql } from '../../config/postgres';

/**
 * L<PERSON>y phí vận chuyển cố định từ bảng shipping_fees
 * @param tenant_id ID của tenant
 */
export const getShippingFees = async ({
  tenant_id,
}: {
  tenant_id: string;
}) => {
  try {
    console.log(`[getShippingFee] Lấy phí vận chuyển cố định cho tenant:`, tenant_id);

    // Lấy phí vận chuyển cố định từ bảng shipping_fees
    const fees = await sql`
      SELECT * FROM shipping_fees
      WHERE tenant_id = ${tenant_id}
      AND is_active = true
    `;

    if (!fees || fees.length === 0) {
      console.log(`[getShippingFee] Không tìm thấy phí vận chuyển cho tenant ${tenant_id}`);
      return {
        success: false,
        message: 'Không tìm thấy phí vận chuyển',
      };
    }

    const fee = fees.map(item => ({
      id: item.id,
      name: item.name,
      amount: parseFloat(item.amount) || 0,
      description: item.description,
    }));

    return {
      success: true,
      data: fee,
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy phí vận chuyển:', error);
    return {
      success: false,
      message: `Lỗi khi lấy phí vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy danh sách phương thức vận chuyển có sẵn
 * @param address Thông tin địa chỉ giao hàng (không còn sử dụng để lọc)
 * @param tenant_id ID của tenant
 */
export const getShippingOptions = async ({
  address,
  tenant_id,
}: {
  address: {
    city?: string;
    district?: string;
    province?: string;
    ward?: string;
    country?: string;
  };
  tenant_id: string;
}) => {
  try {
    console.log(`[getShippingOptions] Lấy phương thức vận chuyển cho tenant:`, tenant_id);

    // Lấy tất cả phương thức vận chuyển có sẵn cho tenant
    const methods = await sql`
      SELECT * FROM shipping_methods
      WHERE tenant_id = ${tenant_id}
      AND is_active = true
      ORDER BY sort_order ASC
    `;

    if (!methods || methods.length === 0) {
      console.log(`[getShippingOptions] Không tìm thấy phương thức vận chuyển cho tenant ${tenant_id}`);
      return {
        success: true,
        data: [],
      };
    }

    // Định dạng lại kết quả để phù hợp với cấu trúc dữ liệu hiện tại
    const formattedMethods = methods.map(method => formatShippingMethod(method));

    return {
      success: true,
      data: formattedMethods,
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy phương thức vận chuyển:', error);
    return {
      success: false,
      message: `Lỗi khi lấy phương thức vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Hàm chung để định dạng phương thức vận chuyển
 * @param method Dữ liệu phương thức vận chuyển từ database
 */
const formatShippingMethod = (method: any) => {
  return {
    id: method.id,
    code: method.code || method.id,
    name: method.name,
    description: method.description,
    fee: parseFloat(method.price) || 0,
    estimated_delivery: method.estimated_delivery || "3-5 ngày",
    free_shipping_threshold: method.free_shipping_threshold ? parseFloat(method.free_shipping_threshold) : null,
    minimum_order_amount: method.minimum_order_amount ? parseFloat(method.minimum_order_amount) : 0,
    shipping_type: "fixed", // Luôn sử dụng loại "fixed"
  };
};

/**
 * Lấy thông tin chi tiết của một phương thức vận chuyển theo ID
 * @param shipping_method_id ID của phương thức vận chuyển
 * @param tenant_id ID của tenant
 */
export const getShippingMethodDetails = async ({
  shipping_method_id,
  tenant_id,
}: {
  shipping_method_id: string;
  tenant_id: string;
}) => {
  try {
    console.log(`[getShippingMethodDetails] Lấy thông tin phương thức vận chuyển: ${shipping_method_id}`);

    const method = await sql`
      SELECT * FROM shipping_methods
      WHERE id = ${shipping_method_id}
      AND tenant_id = ${tenant_id}
      AND is_active = true
    `;

    if (!method || method.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy phương thức vận chuyển',
      };
    }

    return {
      success: true,
      data: formatShippingMethod(method[0]),
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin phương thức vận chuyển:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin phương thức vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Lấy thông tin chi tiết của một phương thức vận chuyển theo mã code
 * @param code Mã code của phương thức vận chuyển
 * @param tenant_id ID của tenant
 */
export const getShippingMethodByCode = async ({
  code,
  tenant_id,
}: {
  code: string;
  tenant_id: string;
}) => {
  try {
    console.log(`[getShippingMethodByCode] Lấy thông tin phương thức vận chuyển với mã: ${code}`);

    const method = await sql`
      SELECT * FROM shipping_methods
      WHERE code = ${code}
      AND tenant_id = ${tenant_id}
      AND is_active = true
    `;

    if (!method || method.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy phương thức vận chuyển',
      };
    }

    return {
      success: true,
      data: formatShippingMethod(method[0]),
    };
  } catch (error: any) {
    console.error('Lỗi khi lấy thông tin phương thức vận chuyển:', error);
    return {
      success: false,
      message: `Lỗi khi lấy thông tin phương thức vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};

/**
 * Tính phí vận chuyển dựa trên phương thức vận chuyển và giá trị đơn hàng
 * Chỉ hỗ trợ loại phí vận chuyển cố định (fixed)
 * @param shipping_method_id ID của phương thức vận chuyển
 * @param order_value Giá trị đơn hàng
 * @param tenant_id ID của tenant
 */
export const calculateShippingFee = async ({
  shipping_method_id,
  order_value,
  tenant_id,
}: {
  shipping_method_id: string;
  order_value: number;
  tenant_id: string;
}) => {
  try {
    console.log(`[calculateShippingFee] Tính phí vận chuyển cho đơn hàng trị giá ${order_value}`);

    const method = await sql`
      SELECT * FROM shipping_methods
      WHERE id = ${shipping_method_id}
      AND tenant_id = ${tenant_id}
      AND is_active = true
    `;

    if (!method || method.length === 0) {
      return {
        success: false,
        message: 'Không tìm thấy phương thức vận chuyển',
      };
    }

    // Định dạng phương thức vận chuyển
    const formattedMethod = formatShippingMethod(method[0]);

    // Phí vận chuyển cố định từ cấu hình
    let shippingFee = formattedMethod.fee;

    // Kiểm tra nếu đơn hàng đạt ngưỡng miễn phí vận chuyển
    if (formattedMethod.free_shipping_threshold !== null && order_value >= formattedMethod.free_shipping_threshold) {
      shippingFee = 0;
    }

    return {
      success: true,
      data: {
        shipping_fee: shippingFee,
        method_name: formattedMethod.name,
        estimated_delivery: formattedMethod.estimated_delivery,
        shipping_type: formattedMethod.shipping_type,
      },
    };
  } catch (error: any) {
    console.error('Lỗi khi tính phí vận chuyển:', error);
    return {
      success: false,
      message: `Lỗi khi tính phí vận chuyển: ${error?.message || 'Lỗi không xác định'}`,
    };
  }
};
