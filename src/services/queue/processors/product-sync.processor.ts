/**
 * <PERSON>or cho các job đồng bộ sản phẩm
 */
import { Job } from 'bullmq';
import { syncAllProductsFromHaravan, syncAllProductsFromSapo } from '../../sync/platform-sync.service';

/**
 * Xử lý job đồng bộ sản phẩm từ Haravan
 * @param job Job từ BullMQ
 */
const processHaravanSync = async (job: Job) => {
  const { token, tenant_id, bot_id, options } = job.data;

  // Cập nhật tiến độ
  await job.updateProgress(20);

  console.log(`Đang đồng bộ sản phẩm từ Haravan cho tenant ${tenant_id}`);

  // Gọi service đồng bộ sản phẩm từ Haravan
  const result = await syncAllProductsFromHaravan(token, tenant_id, bot_id, options);

  // Cập nhật tiến độ
  await job.updateProgress(100);

  return result;
};

/**
 * <PERSON><PERSON> lý job đồng bộ sản phẩm từ Sapo
 * @param job Job từ BullMQ
 */
const processSapoSync = async (job: Job) => {
  const { sapo_url, tenant_id, bot_id, options } = job.data;

  // Cập nhật tiến độ
  await job.updateProgress(20);

  console.log(`Đang đồng bộ sản phẩm từ Sapo cho tenant ${tenant_id}`);

  // Gọi service đồng bộ sản phẩm từ Sapo
  const result = await syncAllProductsFromSapo(sapo_url, tenant_id, bot_id, options);

  // Cập nhật tiến độ
  await job.updateProgress(100);

  return result;
};

/**
 * Hàm xử lý job đồng bộ sản phẩm theo loại
 * @param job Job từ BullMQ
 */
export const processProductSyncJob = async (job: Job) => {
  const { name } = job;

  console.log(`Bắt đầu xử lý job ${name} với ID: ${job.id}`);

  try {
    let result;
    
    switch (name) {
      case 'sync-haravan-products':
        result = await processHaravanSync(job);
        break;
      case 'sync-sapo-products':
        result = await processSapoSync(job);
        break;
      default:
        throw new Error(`Loại job không được hỗ trợ: ${name}`);
    }

    console.log(`Hoàn thành job ${name} với ID: ${job.id}`);
    return result;
  } catch (error: any) {
    console.error(`Lỗi khi xử lý job ${name} với ID: ${job.id}:`, error);
    throw error;
  }
};

export default {
  processProductSyncJob
};
