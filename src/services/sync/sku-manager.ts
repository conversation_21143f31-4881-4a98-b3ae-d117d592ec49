import { supabaseAdmin } from "../../config/supabase";


/**
 * Interface cho SKU counter
 */
interface SkuCounter {
  platform: string;
  tenant_id: string;
  current_counter: number;
  last_updated: string;
}

/**
 * Lấy counter hiện tại cho nền tảng và tenant
 * @param platform Tên nền tảng (haravan, sapo)
 * @param tenant_id ID của tenant
 * @returns Counter hiện tại
 */
export const getCurrentSkuCounter = async (platform: string, tenant_id: string): Promise<number> => {
  try {
    const { data, error } = await supabaseAdmin
      .from('sku_counters')
      .select('current_counter')
      .eq('platform', platform.toLowerCase())
      .eq('tenant_id', tenant_id)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error(`Lỗi khi lấy SKU counter cho ${platform}:`, error);
      return 10001; // Bắt đầu từ 10001
    }

    return data?.current_counter || 10001;
  } catch (error) {
    console.error(`Lỗi không xác đ<PERSON>nh khi lấy SKU counter cho ${platform}:`, error);
    return 10001;
  }
};

/**
 * Cập nhật counter cho nền tảng và tenant
 * @param platform Tên nền tảng (haravan, sapo)
 * @param tenant_id ID của tenant
 * @param newCounter Counter mới
 */
export const updateSkuCounter = async (platform: string, tenant_id: string, newCounter: number): Promise<void> => {
  try {
    const { error } = await supabaseAdmin
      .from('sku_counters')
      .upsert({
        platform: platform.toLowerCase(),
        tenant_id: tenant_id,
        current_counter: newCounter,
        last_updated: new Date().toISOString()
      }, {
        onConflict: 'platform,tenant_id'
      });

    if (error) {
      console.error(`Lỗi khi cập nhật SKU counter cho ${platform}:`, error);
    }
  } catch (error) {
    console.error(`Lỗi không xác định khi cập nhật SKU counter cho ${platform}:`, error);
  }
};

/**
 * Tạo SKU chuẩn hóa với counter tự động tăng
 * @param platform Tên nền tảng (haravan, sapo)
 * @param tenant_id ID của tenant
 * @param sourceId ID nguồn từ nền tảng (optional, dùng làm fallback)
 * @returns SKU đã được chuẩn hóa
 */
export const generateNextSku = async (platform: string, tenant_id: string, sourceId?: string): Promise<string> => {
  const platformPrefixes: { [key: string]: string } = {
    'haravan': 'HARA',
    'sapo': 'SAPO'
  };

  const prefix = platformPrefixes[platform.toLowerCase()] || platform.toUpperCase().substring(0, 4);

  try {
    // Lấy counter hiện tại
    const currentCounter = await getCurrentSkuCounter(platform, tenant_id);
    
    // Tạo SKU với counter hiện tại
    const newSku = `${prefix}${currentCounter.toString().padStart(5, '0')}`;
    
    // Kiểm tra xem SKU đã tồn tại chưa
    const { data: existingProduct } = await supabaseAdmin
      .from('products')
      .select('id')
      .eq('sku', newSku)
      .eq('tenant_id', tenant_id)
      .single();

    if (existingProduct) {
      // Nếu SKU đã tồn tại, tăng counter và thử lại
      const nextCounter = currentCounter + 1;
      await updateSkuCounter(platform, tenant_id, nextCounter);
      return generateNextSku(platform, tenant_id, sourceId);
    }

    // Cập nhật counter cho lần sử dụng tiếp theo
    await updateSkuCounter(platform, tenant_id, currentCounter + 1);
    
    return newSku;
  } catch (error) {
    console.error(`Lỗi khi tạo SKU cho ${platform}:`, error);
    
    // Fallback: sử dụng sourceId nếu có
    if (sourceId) {
      const paddedSourceId = sourceId.toString().padStart(5, '0');
      return `${prefix}${paddedSourceId}`;
    }
    
    // Fallback cuối cùng: sử dụng timestamp
    const timestamp = Date.now().toString().slice(-5);
    return `${prefix}${timestamp}`;
  }
};

/**
 * Tạo SKU cho biến thể dựa trên SKU sản phẩm cha
 * @param productSku SKU của sản phẩm cha
 * @param variantIndex Chỉ số của biến thể
 * @param variantAttributes Thuộc tính của biến thể
 * @returns SKU của biến thể
 */
export const generateVariantSku = (productSku: string, variantIndex: number, variantAttributes?: Record<string, string>): string => {
  // Tạo suffix từ thuộc tính biến thể
  let suffix = '';
  if (variantAttributes && Object.keys(variantAttributes).length > 0) {
    const attributeValues = Object.values(variantAttributes)
      .map(value => value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase())
      .join('');
    suffix = attributeValues.substring(0, 3); // Lấy 3 ký tự đầu
  }
  
  if (!suffix) {
    // Nếu không có thuộc tính, sử dụng chỉ số biến thể
    suffix = (variantIndex + 1).toString().padStart(2, '0');
  }
  
  return `${productSku}-${suffix}`;
};

/**
 * Kiểm tra và chuẩn hóa SKU hiện có
 * @param sku SKU cần kiểm tra
 * @param platform Tên nền tảng
 * @param tenant_id ID của tenant
 * @param sourceId ID nguồn (optional)
 * @returns SKU đã được chuẩn hóa
 */
export const validateAndNormalizeSku = async (sku: string, platform: string, tenant_id: string, sourceId?: string): Promise<string> => {
  // Nếu SKU đã đúng định dạng chuẩn, giữ nguyên
  if (sku && sku.match(/^(HARA|SAPO)\d{5}$/)) {
    return sku;
  }

  // Nếu SKU không đúng định dạng hoặc rỗng, tạo mới
  return generateNextSku(platform, tenant_id, sourceId);
};

/**
 * Khởi tạo bảng sku_counters nếu chưa tồn tại
 */
export const initializeSkuCounters = async (): Promise<void> => {
  try {
    // Kiểm tra xem bảng đã tồn tại chưa
    const { error } = await supabaseAdmin
      .from('sku_counters')
      .select('platform')
      .limit(1);

    if (error && error.code === '42P01') {
      console.log('Bảng sku_counters chưa tồn tại, cần tạo thủ công trong Supabase');
      console.log(`
        CREATE TABLE IF NOT EXISTS sku_counters (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          platform VARCHAR(50) NOT NULL,
          tenant_id UUID NOT NULL,
          current_counter INTEGER NOT NULL DEFAULT 10001,
          last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(platform, tenant_id)
        );
      `);
    }
  } catch (error) {
    console.error('Lỗi khi khởi tạo sku_counters:', error);
  }
};

export default {
  getCurrentSkuCounter,
  updateSkuCounter,
  generateNextSku,
  generateVariantSku,
  validateAndNormalizeSku,
  initializeSkuCounters
};
