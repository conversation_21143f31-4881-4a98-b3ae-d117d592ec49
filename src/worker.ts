/**
 * Dedicated Worker Process for BullMQ
 * Chạy riêng biệt với Express server theo best practices 2025
 */
import dotenv from "dotenv";
import { startAllWorkers, stopAllWorkers } from "./services/queue/workers";

// Khởi tạo biến môi trường
dotenv.config();

const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_PRODUCTION = NODE_ENV === 'production';

/**
 * Khởi động worker process
 */
const startWorkerProcess = async () => {
  try {
    console.log(`🚀 Starting BullMQ Worker Process in ${NODE_ENV} mode...`);
    
    // Khởi động tất cả workers
    await startAllWorkers();
    
    console.log('✅ BullMQ Worker Process đã sẵn sàng!');
    console.log('🔄 Workers đang lắng nghe jobs...');
    
    if (!IS_PRODUCTION) {
      console.log('📊 Worker process running in development mode');
      console.log('🔧 Logs enabled for debugging');
    } else {
      console.log('🏭 Worker process running in production mode');
      console.log('🔇 Minimal logging enabled');
    }
    
  } catch (error: any) {
    console.error('❌ Failed to start worker process:', error);
    process.exit(1);
  }
};

/**
 * Graceful shutdown
 */
const gracefulShutdown = async () => {
  console.log("\n🛑 Received shutdown signal, closing workers gracefully...");
  
  try {
    await stopAllWorkers();
    console.log("✅ Workers closed successfully");
    process.exit(0);
  } catch (error) {
    console.error("❌ Error during worker shutdown:", error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception in worker process:', error);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection in worker process:', reason);
  gracefulShutdown();
});

// Start the worker process
startWorkerProcess().catch((error) => {
  console.error("❌ Unhandled error during worker startup:", error);
  process.exit(1);
});
