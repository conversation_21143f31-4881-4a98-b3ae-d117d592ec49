# Thư mục node_modules
node_modules

# <PERSON><PERSON><PERSON> mục build
dist

# <PERSON><PERSON><PERSON> mục logs
logs
*.log
npm-debug.log*

# Thư mục coverage
coverage

# Các file môi trường (sẽ được cung cấp thông qua biến môi trường)
.env
.env.*
!.env.example

# Các file Git
.git
.gitignore

# Các file Docker
Dockerfile
docker-compose.yml
.dockerignore

# Các file tạm thời
.DS_Store
*.swp
*.swo

# Các file IDE
.idea
.vscode
*.sublime-project
*.sublime-workspace

# Các file test
__tests__
test
tests
*.test.js
*.spec.js

# Các file README và tài liệu
README.md
CHANGELOG.md
LICENSE

# Các file cấu hình không cần thiết
.eslintrc
.prettierrc
jest.config.js
tsconfig.tsbuildinfo
