/**
 * File kiểm tra trạng thái của productSyncQueue
 */
const { Queue, Worker } = require('bullmq');
const dotenv = require('dotenv');

// Load biến môi trường
dotenv.config();

// Cấu hình kết nối Redis/Dragonfly
const redisConfig = {
  connection: {
    host: process.env.REDIS_HOST || '*************',
    port: parseInt(process.env.REDIS_PORT || '6361'),
    password: process.env.REDIS_PASSWORD || 'tvqbjr0pnrnrluxtf9zdqkvgtoykpkb0',
  },
  prefix: '{mooly-queue}'
};

// Tên queue
const queueName = '{product-sync}';

// Hàm kiểm tra queue
async function checkQueue() {
  try {
    console.log('Thông tin kết nối Redis/Dragonfly:');
    console.log(`Host: ${redisConfig.connection.host}`);
    console.log(`Port: ${redisConfig.connection.port}`);
    console.log(`Password: ${redisConfig.connection.password ? '***' : 'không có'}`);
    console.log(`Prefix: ${redisConfig.prefix}`);
    console.log(`Queue name: ${queueName}`);
    
    // Tạo kết nối đến queue
    const queue = new Queue(queueName, redisConfig);
    
    console.log('Đã kết nối đến queue thành công');
    
    // Lấy thông tin về các job trong queue
    const jobCounts = await queue.getJobCounts();
    console.log('Số lượng job trong queue:', jobCounts);
    
    // Lấy danh sách các job đang chờ xử lý
    const waitingJobs = await queue.getJobs(['waiting'], 0, 10);
    console.log('Jobs đang chờ xử lý:');
    for (const job of waitingJobs) {
      console.log(`- ID: ${job.id}, Name: ${job.name}, Data:`, job.data);
    }
    
    // Lấy danh sách các job đang xử lý
    const activeJobs = await queue.getJobs(['active'], 0, 10);
    console.log('Jobs đang xử lý:');
    for (const job of activeJobs) {
      console.log(`- ID: ${job.id}, Name: ${job.name}, Data:`, job.data);
    }
    
    // Lấy danh sách các job đã hoàn thành gần đây
    const completedJobs = await queue.getJobs(['completed'], 0, 10);
    console.log('Jobs đã hoàn thành gần đây:');
    for (const job of completedJobs) {
      console.log(`- ID: ${job.id}, Name: ${job.name}, Result:`, job.returnvalue);
    }
    
    // Lấy danh sách các job thất bại gần đây
    const failedJobs = await queue.getJobs(['failed'], 0, 10);
    console.log('Jobs thất bại gần đây:');
    for (const job of failedJobs) {
      console.log(`- ID: ${job.id}, Name: ${job.name}, Error:`, job.failedReason);
    }
    
    // Đóng kết nối
    await queue.close();
    console.log('Đã đóng kết nối đến queue');
    
  } catch (error) {
    console.error('Lỗi khi kiểm tra queue:', error);
  }
}

// Chạy hàm kiểm tra
checkQueue().then(() => {
  console.log('Đã hoàn thành kiểm tra queue');
  process.exit(0);
}).catch(error => {
  console.error('Lỗi không xử lý được:', error);
  process.exit(1);
});
