# 🖼️ Cấu Hình Delay Cho Tin Nhắn Hình Ảnh

## 📋 Tổng Quan

Hệ thống đã được cấu hình lại để **delay 10 giây mặc định** khi user gửi hình ảnh, thay vì xử lý ngay lập tức như trước. Điều này giúp:

- ⏰ **Chờ user nhập thêm câu hỏi** để tư vấn chính xác hơn
- 🤖 **Tối ưu hóa vai trò assistant** khi tìm thấy sản phẩm từ hình ảnh
- 💬 **Tạo cuộc trò chuyện tự nhiên** thay vì phản hồi máy móc

## ⚙️ Cấu Hình Mới

### Default Settings (Hard-coded trong Code)
- **Delay Time cho hình ảnh**: 10 giây cố định trong code
- **Delay Time cho text**: <PERSON> cấu hình database (mặc định 0 giây)
- **Immediate Processing**: Chỉ khi có từ khóa khẩn cấp
- **Assistant Role**: Thông tin sản phẩm được gán vai trò assistant

### Code Configuration
```typescript
// Trong message-buffer.service.ts
private getBotTimeout(chatbotInfo: any, hasImages: boolean = false): number {
  // Nếu có hình ảnh, mặc định 10s để chờ user nhập thêm câu hỏi
  if (hasImages) {
    const imageDelaySeconds = 10; // Mặc định 10s cho tin nhắn hình ảnh
    return imageDelaySeconds * 1000;
  }

  // Tin nhắn text sử dụng cấu hình từ database
  const delayTimeSeconds = chatbotInfo?.instruction?.delay_time ?? 0;
  return delayTimeSeconds * 1000;
}
```

## 🔄 Luồng Xử Lý Mới

### Trước (Cũ)
```
User gửi hình ảnh → Xử lý ngay lập tức → Tìm sản phẩm → Phản hồi
```

### Sau (Mới)
```
User gửi hình ảnh → Delay 10s → Chờ thêm tin nhắn → Xử lý tổng hợp
                                      ↓
                              Tìm sản phẩm (role: assistant)
                                      ↓
                              User question hoặc tư vấn mặc định
                                      ↓
                              Phản hồi tự nhiên và chính xác
```

## 🎯 Ví Dụ Conversation Context

### Khi tìm thấy sản phẩm + có tin nhắn text
```json
[
  {
    "role": "assistant",
    "content": "Tôi đã tìm thấy thông tin sản phẩm từ hình ảnh bạn gửi:\n\n1. **iPhone 15 Pro Max**\n   - Giá: 29,990,000 VNĐ\n   - SKU: IP15PM-256GB\n   - Mô tả: Điện thoại thông minh cao cấp..."
  },
  {
    "role": "user", 
    "content": "Sản phẩm này có bảo hành bao lâu?"
  }
]
```

### Khi tìm thấy sản phẩm + không có tin nhắn text
```json
[
  {
    "role": "assistant",
    "content": "Tôi đã tìm thấy thông tin sản phẩm từ hình ảnh bạn gửi:\n\n1. **iPhone 15 Pro Max**..."
  },
  {
    "role": "user",
    "content": "Bạn có thể tư vấn cho tôi về sản phẩm này được không? Tôi muốn biết thêm thông tin chi tiết."
  }
]
```

## 🛠️ Cấu Hình Tùy Chỉnh

### Cập nhật delay_time cho tin nhắn text
```bash
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{
    "bot_id": "your_bot_id",
    "delay_time": 15
  }'
```

**Lưu ý**: Cấu hình này chỉ áp dụng cho tin nhắn text. Tin nhắn hình ảnh luôn delay 10s cố định.

### Tắt delay cho tin nhắn text (xử lý ngay lập tức)
```bash
curl -X PUT http://localhost:3000/api/system/message-batching/config \
  -H "Content-Type: application/json" \
  -d '{
    "bot_id": "your_bot_id",
    "delay_time": 0
  }'
```

**Lưu ý**: Tin nhắn hình ảnh vẫn delay 10s dù `delay_time = 0`.

## 🔍 Monitoring & Debug

### Kiểm tra cấu hình delay
```bash
curl http://localhost:3000/api/system/chatbot/config/INBOX_ID
```

### Logs để theo dõi
```bash
# Image delay configuration (mới)
🖼️ Image message detected - using default delay: 10s (10000ms) for bot uuid

# Text delay configuration
🕐 Bot timeout configuration: 5s (5000ms) for bot uuid
⚡ Bot immediate processing (no delay) for bot uuid

# Image processing
🔍 Tìm kiếm sản phẩm bằng hình ảnh: https://...
✅ Thêm product_id: abc123 vào danh sách tìm kiếm
📦 Lấy thông tin chi tiết cho 1 sản phẩm

# Job processing
⏰ Tạo delayed job (10000ms): job_123 cho batch batch_456
```

## ⚡ Immediate Processing

Tin nhắn vẫn được xử lý ngay lập tức khi:

### Từ khóa khẩn cấp
- `urgent`, `gấp`, `khẩn cấp`, `emergency`
- `hủy đơn`, `cancel order`, `hủy`, `cancel`

### Cấu hình đặc biệt
- `delay_time = 0` (immediate mode)

## 🎯 Lợi Ích

### Trải nghiệm khách hàng
- ✅ Phản hồi chính xác hơn với context đầy đủ
- ✅ Cuộc trò chuyện tự nhiên như với nhân viên thật
- ✅ Tư vấn chi tiết dựa trên câu hỏi cụ thể

### Hiệu quả hệ thống
- ✅ Giảm số lần gọi API không cần thiết
- ✅ Tối ưu hóa memory và processing
- ✅ Vai trò assistant rõ ràng trong conversation

## 🚨 Lưu Ý Quan Trọng

1. **Hình ảnh luôn delay 10s**: Cố định trong code, không thể thay đổi qua database
2. **Tin nhắn text**: Sử dụng cấu hình `delay_time` từ database
3. **Assistant role**: Thông tin sản phẩm được gán đúng vai trò
4. **Fallback tư vấn**: Tự động tạo câu hỏi khi user không nhập text
5. **Từ khóa khẩn cấp**: Vẫn xử lý ngay lập tức kể cả tin nhắn hình ảnh

## 🔧 Troubleshooting

### Hình ảnh vẫn xử lý ngay lập tức
- Kiểm tra logs: `🖼️ Image message detected - using default delay: 10s`
- Đảm bảo không có từ khóa khẩn cấp trong tin nhắn
- Kiểm tra function `shouldProcessImmediately` trong code

### Tin nhắn text không delay đúng
- Kiểm tra `delay_time` trong database
- Xem logs: `🕐 Bot timeout configuration` hoặc `⚡ Bot immediate processing`
- Cập nhật cấu hình qua API nếu cần

### Không tìm thấy sản phẩm từ hình ảnh
- Kiểm tra Weaviate service
- Xem logs: `🔍 Tìm kiếm sản phẩm bằng hình ảnh`
- Đảm bảo `tenant_id` và `bot_id` đúng

### Conversation context không đúng
- Kiểm tra role assignment trong logs
- Xem `buildConversationContext` function
- Đảm bảo product details được format đúng

### Muốn thay đổi delay time cho hình ảnh
- Cần sửa code trong `getBotTimeout` function
- Thay đổi giá trị `imageDelaySeconds = 10` thành giá trị mong muốn
- Deploy lại ứng dụng
